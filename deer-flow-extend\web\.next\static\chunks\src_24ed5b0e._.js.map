{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/deer-flow/rainbow-text.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"animated\": \"rainbow-text-module__D-e5SG__animated\",\n  \"textShine\": \"rainbow-text-module__D-e5SG__textShine\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA"}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/deer-flow/rainbow-text.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport styles from \"./rainbow-text.module.css\";\r\n\r\nexport function RainbowText({\r\n  animated,\r\n  className,\r\n  children,\r\n}: {\r\n  animated?: boolean;\r\n  className?: string;\r\n  children?: React.ReactNode;\r\n}) {\r\n  return (\r\n    <span className={cn(animated && styles.animated, className)}>\r\n      {children}\r\n    </span>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;;;;AAEO,SAAS,YAAY,EAC1B,QAAQ,EACR,SAAS,EACT,QAAQ,EAKT;IACC,qBACE,sSAAC;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY,mKAAA,CAAA,UAAM,CAAC,QAAQ,EAAE;kBAC9C;;;;;;AAGP;KAdgB", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/card.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;;;;;AAI/B;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/core/api/hooks.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { useEffect, useRef, useState } from \"react\";\r\n\r\nimport { useReplay } from \"../replay\";\r\n\r\nimport { fetchReplayTitle } from \"./chat\";\r\n\r\nexport function useReplayMetadata() {\r\n  const { isReplay } = useReplay();\r\n  const [title, setTitle] = useState<string | null>(null);\r\n  const isLoading = useRef(false);\r\n  const [error, setError] = useState<boolean>(false);\r\n  useEffect(() => {\r\n    if (!isReplay) {\r\n      return;\r\n    }\r\n    if (title || isLoading.current) {\r\n      return;\r\n    }\r\n    isLoading.current = true;\r\n    fetchReplayTitle()\r\n      .then((title) => {\r\n        setError(false);\r\n        setTitle(title ?? null);\r\n        if (title) {\r\n          document.title = `${title} - DeerFlow`;\r\n        }\r\n      })\r\n      .catch(() => {\r\n        setError(true);\r\n        setTitle(\"Error: the replay is not available.\");\r\n        document.title = \"DeerFlow\";\r\n      })\r\n      .finally(() => {\r\n        isLoading.current = false;\r\n      });\r\n  }, [isLoading, isReplay, title]);\r\n  return { title, isLoading, hasError: error };\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;AAEA;AAAA;AAEA;;;;;AAEO,SAAS;;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,YAAY,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAW;IAC5C,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,UAAU;gBACb;YACF;YACA,IAAI,SAAS,UAAU,OAAO,EAAE;gBAC9B;YACF;YACA,UAAU,OAAO,GAAG;YACpB,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD,IACZ,IAAI;+CAAC,CAAC;oBACL,SAAS;oBACT,SAAS,SAAS;oBAClB,IAAI,OAAO;wBACT,SAAS,KAAK,GAAG,GAAG,MAAM,WAAW,CAAC;oBACxC;gBACF;8CACC,KAAK;+CAAC;oBACL,SAAS;oBACT,SAAS;oBACT,SAAS,KAAK,GAAG;gBACnB;8CACC,OAAO;+CAAC;oBACP,UAAU,OAAO,GAAG;gBACtB;;QACJ;sCAAG;QAAC;QAAW;QAAU;KAAM;IAC/B,OAAO;QAAE;QAAO;QAAW,UAAU;IAAM;AAC7C;GA/BgB;;QACO,iIAAA,CAAA,YAAS", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/chat/components/welcome.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { motion } from \"framer-motion\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport function Welcome({ className }: { className?: string }) {\r\n  return (\r\n    <motion.div\r\n      className={cn(\"flex flex-col\", className)}\r\n      style={{ transition: \"all 0.2s ease-out\" }}\r\n      initial={{ opacity: 0, scale: 0.85 }}\r\n      animate={{ opacity: 1, scale: 1 }}\r\n    >\r\n      <h3 className=\"mb-2 text-center text-3xl font-medium\">\r\n        👋 Hello, there!\r\n      </h3>\r\n      <div className=\"text-muted-foreground px-4 text-center text-lg\">\r\n        Welcome to{\" \"}\r\n        <a\r\n          href=\"https://github.com/bytedance/deer-flow\"\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          className=\"hover:underline\"\r\n        >\r\n          🦌 <PERSON><PERSON><PERSON>\r\n        </a>\r\n        , a deep research assistant built on cutting-edge language models, helps\r\n        you search on web, browse information, and handle complex tasks.\r\n      </div>\r\n    </motion.div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;;;;AAEO,SAAS,QAAQ,EAAE,SAAS,EAA0B;IAC3D,qBACE,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC/B,OAAO;YAAE,YAAY;QAAoB;QACzC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAK;QACnC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;;0BAEhC,sSAAC;gBAAG,WAAU;0BAAwC;;;;;;0BAGtD,sSAAC;gBAAI,WAAU;;oBAAiD;oBACnD;kCACX,sSAAC;wBACC,MAAK;wBACL,QAAO;wBACP,KAAI;wBACJ,WAAU;kCACX;;;;;;oBAEG;;;;;;;;;;;;;AAMZ;KA1BgB", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/chat/components/conversation-starter.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { motion } from \"framer-motion\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport { Welcome } from \"./welcome\";\r\n\r\nconst questions = [\r\n  // Research questions\r\n  \"How many times taller is the Eiffel Tower than the tallest building in the world?\",\r\n  \"How many years does an average Tesla battery last compared to a gasoline engine?\",\r\n  \"How many liters of water are required to produce 1 kg of beef?\",\r\n  \"How many times faster is the speed of light compared to the speed of sound?\",\r\n  \r\n  // KB building examples\r\n  \"Build knowledge base on artificial intelligence trends\",\r\n  \"Gather 30 articles about renewable energy technology\",\r\n  \"Create knowledge base on cybersecurity best practices\",\r\n  \"Collect knowledge about blockchain applications in finance\",\r\n];\r\n\r\nexport function ConversationStarter({\r\n  className,\r\n  onSend,\r\n}: {\r\n  className?: string;\r\n  onSend?: (message: string) => void;\r\n}) {\r\n  return (\r\n    <div className={cn(\"flex flex-col items-center\", className)}>\r\n      <div className=\"pointer-events-none fixed inset-0 flex items-center justify-center\">\r\n        <Welcome className=\"pointer-events-auto mb-15 w-[75%] -translate-y-24\" />\r\n      </div>\r\n      <ul className=\"flex flex-wrap\">\r\n        {questions.map((question, index) => (\r\n          <motion.li\r\n            key={question}\r\n            className=\"flex w-1/2 shrink-0 p-2 active:scale-105\"\r\n            style={{ transition: \"all 0.2s ease-out\" }}\r\n            initial={{ opacity: 0, y: 24 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            exit={{ opacity: 0, y: -20 }}\r\n            transition={{\r\n              duration: 0.2,\r\n              delay: index * 0.1 + 0.5,\r\n              ease: \"easeOut\",\r\n            }}\r\n          >\r\n            <div\r\n              className={cn(\r\n                \"bg-card text-muted-foreground cursor-pointer rounded-2xl border px-4 py-4 opacity-75 transition-all duration-300 hover:opacity-100 hover:shadow-md\",\r\n                // Add visual distinction for KB building questions\r\n                question.includes(\"knowledge base\") || question.includes(\"Build\") || question.includes(\"Gather\") || question.includes(\"Collect\") \r\n                  ? \"border-blue-200 hover:border-blue-300 hover:bg-blue-50/50\" \r\n                  : \"\"\r\n              )}\r\n              onClick={() => {\r\n                onSend?.(question);\r\n              }}\r\n            >\r\n              <div className=\"flex items-start gap-2\">\r\n                {/* Add icon for KB building questions */}\r\n                {(question.includes(\"knowledge base\") || question.includes(\"Build\") || question.includes(\"Gather\") || question.includes(\"Collect\")) && (\r\n                  <span className=\"text-blue-500 text-sm\">🗃️</span>\r\n                )}\r\n                <span>{question}</span>\r\n              </div>\r\n            </div>\r\n          </motion.li>\r\n        ))}\r\n      </ul>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;AAEA;;;;;AAEA,MAAM,YAAY;IAChB,qBAAqB;IACrB;IACA;IACA;IACA;IAEA,uBAAuB;IACvB;IACA;IACA;IACA;CACD;AAEM,SAAS,oBAAoB,EAClC,SAAS,EACT,MAAM,EAIP;IACC,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;;0BAC/C,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,+IAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;0BAErB,sSAAC;gBAAG,WAAU;0BACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;wBAER,WAAU;wBACV,OAAO;4BAAE,YAAY;wBAAoB;wBACzC,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,YAAY;4BACV,UAAU;4BACV,OAAO,QAAQ,MAAM;4BACrB,MAAM;wBACR;kCAEA,cAAA,sSAAC;4BACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA,mDAAmD;4BACnD,SAAS,QAAQ,CAAC,qBAAqB,SAAS,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC,aAAa,SAAS,QAAQ,CAAC,aAClH,8DACA;4BAEN,SAAS;gCACP,SAAS;4BACX;sCAEA,cAAA,sSAAC;gCAAI,WAAU;;oCAEZ,CAAC,SAAS,QAAQ,CAAC,qBAAqB,SAAS,QAAQ,CAAC,YAAY,SAAS,QAAQ,CAAC,aAAa,SAAS,QAAQ,CAAC,UAAU,mBAChI,sSAAC;wCAAK,WAAU;kDAAwB;;;;;;kDAE1C,sSAAC;kDAAM;;;;;;;;;;;;;;;;;uBA7BN;;;;;;;;;;;;;;;;AAqCjB;KApDgB", "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/deer-flow/icons/detective.tsx"], "sourcesContent": ["export function Detective({ className }: { className?: string }) {\r\n  return (\r\n    <svg\r\n      className={className}\r\n      version=\"1.1\"\r\n      width=\"800px\"\r\n      height=\"800px\"\r\n      viewBox=\"0 0 512 512\"\r\n    >\r\n      <g fill=\"currentcolor\">\r\n        <path\r\n          d=\"M392.692,257.322c-1.172-8.125-2.488-16.98-3.807-25.984c-5.856-39.012-12.59-81.688-14.86-87.832\r\n\t\tc-4.318-11.715-18.371-44.723-68.217-25.984c-15.738,5.926-18.812,11.93-41.648,8.93c-17.273-2.27-28.326-15.59-52.336-24.668\r\n\t\tc-49.844-18.883-71.584,11.711-75.902,23.422c-2.27,6.148-9.004,67.121-14.86,106.133c-1.39,8.86-2.633,17.566-3.804,25.621\r\n\t\tc37.256,7.535,84.174,12.879,138.705,12.879C309.541,269.837,355.801,264.716,392.692,257.322z\"\r\n        />\r\n        <path\r\n          d=\"M443.707,306.509c-8.051-2.196-16.834-4.246-26.057-6.148c-1.83-0.805-3.66-1.535-5.49-2.27h-0.072\r\n\t\tc-46.918,10.394-102.254,15.664-156.125,15.664c-53.652,0-108.768-5.27-155.541-15.516c-1.316,0.512-2.707,1.098-4.098,1.684\r\n\t\tc-8.858,1.828-17.348,3.73-25.106,5.781l-0.148,0.074C27.008,317.49,0,333.372,0,350.939c0,36.012,114.549,65.289,256.035,65.289\r\n\t\tc141.34,0,255.965-29.278,255.965-65.289C512,333.74,486.016,318.22,443.707,306.509z\"\r\n        />\r\n      </g>\r\n    </svg>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAO,SAAS,UAAU,EAAE,SAAS,EAA0B;IAC7D,qBACE,sSAAC;QACC,WAAW;QACX,SAAQ;QACR,OAAM;QACN,QAAO;QACP,SAAQ;kBAER,cAAA,sSAAC;YAAE,MAAK;;8BACN,sSAAC;oBACC,GAAE;;;;;;8BAKJ,sSAAC;oBACC,GAAE;;;;;;;;;;;;;;;;;AAQZ;KAzBgB", "debugId": null}}, {"offset": {"line": 499, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/chat/components/input-box.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { AnimatePresence, motion } from \"framer-motion\";\r\nimport { ArrowUp, X, Database, Search } from \"lucide-react\";\r\nimport {\r\n  type KeyboardEvent,\r\n  useCallback,\r\n  useEffect,\r\n  useRef,\r\n  useState,\r\n} from \"react\";\r\n\r\nimport { Detective } from \"~/components/deer-flow/icons/detective\";\r\nimport { Tooltip } from \"~/components/deer-flow/tooltip\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport type { Option } from \"~/core/messages\";\r\nimport {\r\n  setEnableBackgroundInvestigation,\r\n  useSettingsStore,\r\n} from \"~/core/store\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport function InputBox({\r\n  className,\r\n  size,\r\n  responding,\r\n  feedback,\r\n  onSend,\r\n  onCancel,\r\n  onRemoveFeedback,\r\n}: {\r\n  className?: string;\r\n  size?: \"large\" | \"normal\";\r\n  responding?: boolean;\r\n  feedback?: { option: Option } | null;\r\n  onSend?: (message: string, options?: { interruptFeedback?: string }) => void;\r\n  onCancel?: () => void;\r\n  onRemoveFeedback?: () => void;\r\n}) {\r\n  const [message, setMessage] = useState(\"\");\r\n  const [imeStatus, setImeStatus] = useState<\"active\" | \"inactive\">(\"inactive\");\r\n  const [indent, setIndent] = useState(0);\r\n  const [chatMode, setChatMode] = useState<'research' | 'kb_building'>('research');\r\n  const backgroundInvestigation = useSettingsStore(\r\n    (state) => state.general.enableBackgroundInvestigation,\r\n  );\r\n  const textareaRef = useRef<HTMLTextAreaElement>(null);\r\n  const feedbackRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    if (feedback) {\r\n      setMessage(\"\");\r\n\r\n      setTimeout(() => {\r\n        if (feedbackRef.current) {\r\n          setIndent(feedbackRef.current.offsetWidth);\r\n        }\r\n      }, 200);\r\n    }\r\n    setTimeout(() => {\r\n      textareaRef.current?.focus();\r\n    }, 0);\r\n  }, [feedback]);\r\n\r\n  const handleSendMessage = useCallback(() => {\r\n    if (responding) {\r\n      onCancel?.();\r\n    } else {\r\n      if (message.trim() === \"\") {\r\n        return;\r\n      }\r\n      if (onSend) {\r\n        // Add mode prefix for KB building\r\n        const messageWithMode = chatMode === 'kb_building' \r\n          ? `build knowledge base on ${message}`\r\n          : message;\r\n        \r\n        onSend(messageWithMode, {\r\n          interruptFeedback: feedback?.option.value,\r\n        });\r\n        setMessage(\"\");\r\n        onRemoveFeedback?.();\r\n      }\r\n    }\r\n  }, [responding, onCancel, message, onSend, feedback, onRemoveFeedback, chatMode]);\r\n\r\n  const handleKeyDown = useCallback(\r\n    (event: KeyboardEvent<HTMLTextAreaElement>) => {\r\n      if (responding) {\r\n        return;\r\n      }\r\n      if (\r\n        event.key === \"Enter\" &&\r\n        !event.shiftKey &&\r\n        !event.metaKey &&\r\n        !event.ctrlKey &&\r\n        imeStatus === \"inactive\"\r\n      ) {\r\n        event.preventDefault();\r\n        handleSendMessage();\r\n      }\r\n    },\r\n    [responding, imeStatus, handleSendMessage],\r\n  );\r\n\r\n  const getPlaceholderText = () => {\r\n    if (feedback) {\r\n      return `Describe how you ${feedback.option.text.toLocaleLowerCase()}?`;\r\n    }\r\n    \r\n    if (chatMode === 'kb_building') {\r\n      return \"What topics should I build a knowledge base on?\";\r\n    }\r\n    \r\n    return \"What can I do for you?\";\r\n  };\r\n\r\n  return (\r\n    <div className={cn(\"bg-card relative rounded-[24px] border\", className)}>\r\n      <div className=\"w-full\">\r\n        <AnimatePresence>\r\n          {feedback && (\r\n            <motion.div\r\n              ref={feedbackRef}\r\n              className=\"bg-background border-brand absolute top-0 left-0 mt-3 ml-2 flex items-center justify-center gap-1 rounded-2xl border px-2 py-0.5\"\r\n              initial={{ opacity: 0, scale: 0 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              exit={{ opacity: 0, scale: 0 }}\r\n              transition={{ duration: 0.2, ease: \"easeInOut\" }}\r\n            >\r\n              <div className=\"text-brand flex h-full w-full items-center justify-center text-sm opacity-90\">\r\n                {feedback.option.text}\r\n              </div>\r\n              <X\r\n                className=\"cursor-pointer opacity-60\"\r\n                size={16}\r\n                onClick={onRemoveFeedback}\r\n              />\r\n            </motion.div>\r\n          )}\r\n        </AnimatePresence>\r\n        <textarea\r\n          ref={textareaRef}\r\n          className={cn(\r\n            \"m-0 w-full resize-none border-none px-4 py-3 text-lg\",\r\n            size === \"large\" ? \"min-h-32\" : \"min-h-4\",\r\n          )}\r\n          style={{ textIndent: feedback ? `${indent}px` : 0 }}\r\n          placeholder={getPlaceholderText()}\r\n          value={message}\r\n          onCompositionStart={() => setImeStatus(\"active\")}\r\n          onCompositionEnd={() => setImeStatus(\"inactive\")}\r\n          onKeyDown={handleKeyDown}\r\n          onChange={(event) => {\r\n            setMessage(event.target.value);\r\n          }}\r\n        />\r\n      </div>\r\n      <div className=\"flex items-center px-4 py-2\">\r\n        <div className=\"flex grow items-center gap-2\">\r\n          <Tooltip\r\n            className=\"max-w-60\"\r\n            title={\r\n              <div>\r\n                <h3 className=\"mb-2 font-bold\">\r\n                  Investigation Mode: {backgroundInvestigation ? \"On\" : \"Off\"}\r\n                </h3>\r\n                <p>\r\n                  When enabled, DeerFlow will perform a quick search before\r\n                  planning. This is useful for researches related to ongoing\r\n                  events and news.\r\n                </p>\r\n              </div>\r\n            }\r\n          >\r\n            <Button\r\n              className={cn(\r\n                \"rounded-2xl\",\r\n                backgroundInvestigation && \"!border-brand !text-brand\",\r\n              )}\r\n              variant=\"outline\"\r\n              size=\"lg\"\r\n              onClick={() =>\r\n                setEnableBackgroundInvestigation(!backgroundInvestigation)\r\n              }\r\n            >\r\n              <Detective /> Investigation\r\n            </Button>\r\n          </Tooltip>\r\n          \r\n          {/* Mode Toggle */}\r\n          <div className=\"flex items-center gap-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1\">\r\n            <Button\r\n              variant={chatMode === 'research' ? 'default' : 'ghost'}\r\n              size=\"sm\"\r\n              className=\"px-2 py-1 h-8 text-xs\"\r\n              onClick={() => setChatMode('research')}\r\n            >\r\n              <Search size={12} className=\"mr-1\" />\r\n              Research\r\n            </Button>\r\n            <Button\r\n              variant={chatMode === 'kb_building' ? 'default' : 'ghost'}\r\n              size=\"sm\" \r\n              className=\"px-2 py-1 h-8 text-xs\"\r\n              onClick={() => setChatMode('kb_building')}\r\n            >\r\n              <Database size={12} className=\"mr-1\" />\r\n              KB Build\r\n            </Button>\r\n          </div>\r\n\r\n          {/* Mode Indicator */}\r\n          {chatMode === 'kb_building' && (\r\n            <div className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\r\n              Knowledge Base Mode\r\n            </div>\r\n          )}\r\n        </div>\r\n        <div className=\"flex shrink-0 items-center gap-2\">\r\n          <Tooltip title={responding ? \"Stop\" : \"Send\"}>\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"icon\"\r\n              className={cn(\"h-10 w-10 rounded-full\")}\r\n              onClick={handleSendMessage}\r\n            >\r\n              {responding ? (\r\n                <div className=\"flex h-10 w-10 items-center justify-center\">\r\n                  <div className=\"bg-foreground h-4 w-4 rounded-sm opacity-70\" />\r\n                </div>\r\n              ) : (\r\n                <ArrowUp />\r\n              )}\r\n            </Button>\r\n          </Tooltip>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAQA;AACA;AACA;AAEA;AAAA;AAIA;;;;;;;;;;;AAEO,SAAS,SAAS,EACvB,SAAS,EACT,IAAI,EACJ,UAAU,EACV,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,gBAAgB,EASjB;;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAyB;IAClE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA8B;IACrE,MAAM,0BAA0B,CAAA,GAAA,4IAAA,CAAA,mBAAgB,AAAD;8DAC7C,CAAC,QAAU,MAAM,OAAO,CAAC,6BAA6B;;IAExD,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAuB;IAChD,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,UAAU;gBACZ,WAAW;gBAEX;0CAAW;wBACT,IAAI,YAAY,OAAO,EAAE;4BACvB,UAAU,YAAY,OAAO,CAAC,WAAW;wBAC3C;oBACF;yCAAG;YACL;YACA;sCAAW;oBACT,YAAY,OAAO,EAAE;gBACvB;qCAAG;QACL;6BAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAAE;YACpC,IAAI,YAAY;gBACd;YACF,OAAO;gBACL,IAAI,QAAQ,IAAI,OAAO,IAAI;oBACzB;gBACF;gBACA,IAAI,QAAQ;oBACV,kCAAkC;oBAClC,MAAM,kBAAkB,aAAa,gBACjC,CAAC,wBAAwB,EAAE,SAAS,GACpC;oBAEJ,OAAO,iBAAiB;wBACtB,mBAAmB,UAAU,OAAO;oBACtC;oBACA,WAAW;oBACX;gBACF;YACF;QACF;kDAAG;QAAC;QAAY;QAAU;QAAS;QAAQ;QAAU;QAAkB;KAAS;IAEhF,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;+CAC9B,CAAC;YACC,IAAI,YAAY;gBACd;YACF;YACA,IACE,MAAM,GAAG,KAAK,WACd,CAAC,MAAM,QAAQ,IACf,CAAC,MAAM,OAAO,IACd,CAAC,MAAM,OAAO,IACd,cAAc,YACd;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;8CACA;QAAC;QAAY;QAAW;KAAkB;IAG5C,MAAM,qBAAqB;QACzB,IAAI,UAAU;YACZ,OAAO,CAAC,iBAAiB,EAAE,SAAS,MAAM,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;QACxE;QAEA,IAAI,aAAa,eAAe;YAC9B,OAAO;QACT;QAEA,OAAO;IACT;IAEA,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;;0BAC3D,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,qSAAA,CAAA,kBAAe;kCACb,0BACC,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;4BACT,KAAK;4BACL,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAC7B,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAY;;8CAE/C,sSAAC;oCAAI,WAAU;8CACZ,SAAS,MAAM,CAAC,IAAI;;;;;;8CAEvB,sSAAC,mRAAA,CAAA,IAAC;oCACA,WAAU;oCACV,MAAM;oCACN,SAAS;;;;;;;;;;;;;;;;;kCAKjB,sSAAC;wBACC,KAAK;wBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA,SAAS,UAAU,aAAa;wBAElC,OAAO;4BAAE,YAAY,WAAW,GAAG,OAAO,EAAE,CAAC,GAAG;wBAAE;wBAClD,aAAa;wBACb,OAAO;wBACP,oBAAoB,IAAM,aAAa;wBACvC,kBAAkB,IAAM,aAAa;wBACrC,WAAW;wBACX,UAAU,CAAC;4BACT,WAAW,MAAM,MAAM,CAAC,KAAK;wBAC/B;;;;;;;;;;;;0BAGJ,sSAAC;gBAAI,WAAU;;kCACb,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,gJAAA,CAAA,UAAO;gCACN,WAAU;gCACV,qBACE,sSAAC;;sDACC,sSAAC;4CAAG,WAAU;;gDAAiB;gDACR,0BAA0B,OAAO;;;;;;;sDAExD,sSAAC;sDAAE;;;;;;;;;;;;0CAQP,cAAA,sSAAC,qIAAA,CAAA,SAAM;oCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,eACA,2BAA2B;oCAE7B,SAAQ;oCACR,MAAK;oCACL,SAAS,IACP,CAAA,GAAA,4IAAA,CAAA,mCAAgC,AAAD,EAAE,CAAC;;sDAGpC,sSAAC,2JAAA,CAAA,YAAS;;;;;wCAAG;;;;;;;;;;;;0CAKjB,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,aAAa,YAAY;wCAC/C,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,YAAY;;0DAE3B,sSAAC,6RAAA,CAAA,SAAM;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAAS;;;;;;;kDAGvC,sSAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,gBAAgB,YAAY;wCAClD,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,YAAY;;0DAE3B,sSAAC,iSAAA,CAAA,WAAQ;gDAAC,MAAM;gDAAI,WAAU;;;;;;4CAAS;;;;;;;;;;;;;4BAM1C,aAAa,+BACZ,sSAAC;gCAAI,WAAU;0CAAuD;;;;;;;;;;;;kCAK1E,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC,gJAAA,CAAA,UAAO;4BAAC,OAAO,aAAa,SAAS;sCACpC,cAAA,sSAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;gCACd,SAAS;0CAER,2BACC,sSAAC;oCAAI,WAAU;8CACb,cAAA,sSAAC;wCAAI,WAAU;;;;;;;;;;yDAGjB,sSAAC,mSAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxB;GA1NgB;;QAqBkB,4IAAA,CAAA,mBAAgB;;;KArBlC", "debugId": null}}, {"offset": {"line": 885, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/deer-flow/loading-animation.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"bouncing-animation\": \"loading-animation-module__o3q3XG__bouncing-animation\",\n  \"loadingAnimation\": \"loading-animation-module__o3q3XG__loadingAnimation\",\n  \"sm\": \"loading-animation-module__o3q3XG__sm\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 896, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/deer-flow/loading-animation.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport styles from \"./loading-animation.module.css\";\r\n\r\nexport function LoadingAnimation({\r\n  className,\r\n  size = \"normal\",\r\n}: {\r\n  className?: string;\r\n  size?: \"normal\" | \"sm\";\r\n}) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        styles.loadingAnimation,\r\n        size === \"sm\" && styles.sm,\r\n        className,\r\n      )}\r\n    >\r\n      <div></div>\r\n      <div></div>\r\n      <div></div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;;;;AAEO,SAAS,iBAAiB,EAC/B,SAAS,EACT,OAAO,QAAQ,EAIhB;IACC,qBACE,sSAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wKAAA,CAAA,UAAM,CAAC,gBAAgB,EACvB,SAAS,QAAQ,wKAAA,CAAA,UAAM,CAAC,EAAE,EAC1B;;0BAGF,sSAAC;;;;;0BACD,sSAAC;;;;;0BACD,sSAAC;;;;;;;;;;;AAGP;KApBgB", "debugId": null}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/deer-flow/rolling-text.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { motion, AnimatePresence } from \"framer-motion\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport function RollingText({\r\n  className,\r\n  children,\r\n}: {\r\n  className?: string;\r\n  children?: string | string[];\r\n}) {\r\n  return (\r\n    <span\r\n      className={cn(\r\n        \"relative flex h-[2em] items-center overflow-hidden\",\r\n        className,\r\n      )}\r\n    >\r\n      <AnimatePresence mode=\"popLayout\">\r\n        <motion.div\r\n          className=\"absolute w-fit\"\r\n          style={{ transition: \"all 0.3s ease-in-out\" }}\r\n          initial={{ y: \"100%\", opacity: 0 }}\r\n          animate={{ y: \"0%\", opacity: 1 }}\r\n          exit={{ y: \"-100%\", opacity: 0 }}\r\n          transition={{ duration: 0.3, ease: \"easeInOut\" }}\r\n        >\r\n          {children}\r\n        </motion.div>\r\n      </AnimatePresence>\r\n    </span>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AAEA;;;;AAEO,SAAS,YAAY,EAC1B,SAAS,EACT,QAAQ,EAIT;IACC,qBACE,sSAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;kBAGF,cAAA,sSAAC,qSAAA,CAAA,kBAAe;YAAC,MAAK;sBACpB,cAAA,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBAAE,YAAY;gBAAuB;gBAC5C,SAAS;oBAAE,GAAG;oBAAQ,SAAS;gBAAE;gBACjC,SAAS;oBAAE,GAAG;oBAAM,SAAS;gBAAE;gBAC/B,MAAM;oBAAE,GAAG;oBAAS,SAAS;gBAAE;gBAC/B,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAY;0BAE9C;;;;;;;;;;;;;;;;AAKX;KA5BgB", "debugId": null}}, {"offset": {"line": 1012, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\";\r\nimport * as React from \"react\";\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nfunction ScrollArea({\r\n  className,\r\n  children,\r\n  ref,\r\n  ...props\r\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\r\n  return (\r\n    <ScrollAreaPrimitive.Root\r\n      data-slot=\"scroll-area\"\r\n      className={cn(\"relative\", className)}\r\n      {...props}\r\n    >\r\n      <ScrollAreaPrimitive.Viewport\r\n        data-slot=\"scroll-area-viewport\"\r\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\r\n        // https://github.com/stackblitz-labs/use-stick-to-bottom/issues/15\r\n        ref={ref}\r\n      >\r\n        {children}\r\n      </ScrollAreaPrimitive.Viewport>\r\n      <ScrollBar />\r\n      <ScrollAreaPrimitive.Corner />\r\n    </ScrollAreaPrimitive.Root>\r\n  );\r\n}\r\n\r\nfunction ScrollBar({\r\n  className,\r\n  orientation = \"vertical\",\r\n  ...props\r\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\r\n  return (\r\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n      data-slot=\"scroll-area-scrollbar\"\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"flex touch-none p-px transition-colors select-none\",\r\n        orientation === \"vertical\" &&\r\n          \"h-full w-2.5 border-l border-l-transparent\",\r\n        orientation === \"horizontal\" &&\r\n          \"h-2.5 flex-col border-t border-t-transparent\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <ScrollAreaPrimitive.ScrollAreaThumb\r\n        data-slot=\"scroll-area-thumb\"\r\n        className=\"bg-border relative flex-1 rounded-full\"\r\n      />\r\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n  );\r\n}\r\n\r\nexport { ScrollArea, ScrollBar };\r\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,EACH,GAAG,OACmD;IACtD,qBACE,sSAAC,oRAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,sSAAC,oRAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;gBACV,mEAAmE;gBACnE,KAAK;0BAEJ;;;;;;0BAEH,sSAAC;;;;;0BACD,sSAAC,oRAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;KAxBS;AA0BT,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,sSAAC,oRAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,sSAAC,oRAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB;MAzBS", "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/deer-flow/scroll-container.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { useEffect, useImperativeHandle, useRef, type ReactNode, type RefObject } from \"react\";\r\nimport { useStickToBottom } from \"use-stick-to-bottom\";\r\n\r\nimport { ScrollArea } from \"~/components/ui/scroll-area\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport interface ScrollContainerProps {\r\n  className?: string;\r\n  children?: ReactNode;\r\n  scrollShadow?: boolean;\r\n  scrollShadowColor?: string;\r\n  autoScrollToBottom?: boolean;\r\n  ref?: RefObject<ScrollContainerRef | null>;\r\n}\r\n\r\nexport interface ScrollContainerRef {\r\n  scrollToBottom(): void;\r\n}\r\n\r\nexport function ScrollContainer({\r\n  className,\r\n  children,\r\n  scrollShadow = true,\r\n  scrollShadowColor = \"var(--background)\",\r\n  autoScrollToBottom = false,\r\n  ref\r\n}: ScrollContainerProps) {\r\n  const { scrollRef, contentRef, scrollToBottom, isAtBottom } = useStickToBottom({ initial: \"instant\" });\r\n  useImperativeHandle(ref, () => ({\r\n    scrollToBottom() {\r\n      if (isAtBottom) {\r\n        scrollToBottom();\r\n      }\r\n    }\r\n  }));\r\n\r\n  const tempScrollRef = useRef<HTMLElement>(null);\r\n  const tempContentRef = useRef<HTMLElement>(null);\r\n  useEffect(() => {\r\n    if (!autoScrollToBottom) {\r\n      tempScrollRef.current = scrollRef.current;\r\n      tempContentRef.current = contentRef.current;\r\n      scrollRef.current = null;\r\n      contentRef.current = null;\r\n    } else if (tempScrollRef.current && tempContentRef.current) {\r\n      scrollRef.current = tempScrollRef.current;\r\n      contentRef.current = tempContentRef.current;\r\n    }\r\n  }, [autoScrollToBottom, contentRef, scrollRef]);\r\n\r\n  return (\r\n    <div className={cn(\"relative\", className)}>\r\n      {scrollShadow && (\r\n        <>\r\n          <div\r\n            className={cn(\r\n              \"absolute top-0 right-0 left-0 z-10 h-10 bg-gradient-to-t\",\r\n              `from-transparent to-[var(--scroll-shadow-color)]`,\r\n            )}\r\n            style={\r\n              {\r\n                \"--scroll-shadow-color\": scrollShadowColor,\r\n              } as React.CSSProperties\r\n            }\r\n          ></div>\r\n          <div\r\n            className={cn(\r\n              \"absolute right-0 bottom-0 left-0 z-10 h-10 bg-gradient-to-b\",\r\n              `from-transparent to-[var(--scroll-shadow-color)]`,\r\n            )}\r\n            style={\r\n              {\r\n                \"--scroll-shadow-color\": scrollShadowColor,\r\n              } as React.CSSProperties\r\n            }\r\n          ></div>\r\n        </>\r\n      )}\r\n      <ScrollArea ref={scrollRef} className=\"h-full w-full\">\r\n        <div className=\"h-fit w-full\" ref={contentRef}>\r\n          {children}\r\n        </div>\r\n      </ScrollArea>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AAEA;AACA;;;;;;;AAeO,SAAS,gBAAgB,EAC9B,SAAS,EACT,QAAQ,EACR,eAAe,IAAI,EACnB,oBAAoB,mBAAmB,EACvC,qBAAqB,KAAK,EAC1B,GAAG,EACkB;;IACrB,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,0QAAA,CAAA,mBAAgB,AAAD,EAAE;QAAE,SAAS;IAAU;IACpG,CAAA,GAAA,sQAAA,CAAA,sBAAmB,AAAD,EAAE;+CAAK,IAAM,CAAC;gBAC9B;oBACE,IAAI,YAAY;wBACd;oBACF;gBACF;YACF,CAAC;;IAED,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAe;IAC1C,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAe;IAC3C,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,oBAAoB;gBACvB,cAAc,OAAO,GAAG,UAAU,OAAO;gBACzC,eAAe,OAAO,GAAG,WAAW,OAAO;gBAC3C,UAAU,OAAO,GAAG;gBACpB,WAAW,OAAO,GAAG;YACvB,OAAO,IAAI,cAAc,OAAO,IAAI,eAAe,OAAO,EAAE;gBAC1D,UAAU,OAAO,GAAG,cAAc,OAAO;gBACzC,WAAW,OAAO,GAAG,eAAe,OAAO;YAC7C;QACF;oCAAG;QAAC;QAAoB;QAAY;KAAU;IAE9C,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;YAC5B,8BACC;;kCACE,sSAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA,CAAC,gDAAgD,CAAC;wBAEpD,OACE;4BACE,yBAAyB;wBAC3B;;;;;;kCAGJ,sSAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,CAAC,gDAAgD,CAAC;wBAEpD,OACE;4BACE,yBAAyB;wBAC3B;;;;;;;;0BAKR,sSAAC,6IAAA,CAAA,aAAU;gBAAC,KAAK;gBAAW,WAAU;0BACpC,cAAA,sSAAC;oBAAI,WAAU;oBAAe,KAAK;8BAChC;;;;;;;;;;;;;;;;;AAKX;GAlEgB;;QAQgD,0QAAA,CAAA,mBAAgB;;;KARhE", "debugId": null}}, {"offset": {"line": 1209, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nconst Progress = React.forwardRef<\r\n  React.ElementRef<typeof ProgressPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\r\n>(({ className, value, ...props }, ref) => (\r\n  <ProgressPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative h-2 w-full overflow-hidden rounded-full bg-secondary\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ProgressPrimitive.Indicator\r\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\r\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n    />\r\n  </ProgressPrimitive.Root>\r\n))\r\nProgress.displayName = ProgressPrimitive.Root.displayName\r\n\r\nexport { Progress }"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,sQAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,sSAAC,iRAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,sSAAC,iRAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,iRAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1255, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/chat/components/kb-message-card.tsx"], "sourcesContent": ["// web/src/app/chat/components/kb-message-card.tsx\r\n// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport { LoadingOutlined } from \"@ant-design/icons\";\r\nimport { motion } from \"framer-motion\";\r\nimport { \r\n  Database, \r\n  CheckCircle2, \r\n  XCircle, \r\n  Clock, \r\n  FileText, \r\n  Globe,\r\n  TrendingUp,\r\n  AlertCircle\r\n} from \"lucide-react\";\r\nimport { useMemo } from \"react\";\r\n\r\nimport { RainbowText } from \"~/components/deer-flow/rainbow-text\";\r\nimport { Progress } from \"~/components/ui/progress\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"~/components/ui/card\";\r\nimport { Badge } from \"~/components/ui/badge\";\r\nimport { cn } from \"~/lib/utils\";\r\nimport type { Message } from \"~/core/messages\";\r\n\r\ninterface KnowledgeBaseData {\r\n  type: \"kb_building\";\r\n  request: {\r\n    topics: string[];\r\n    target_article_count: number;\r\n    industries?: string[];\r\n    locale: string;\r\n  };\r\n  buildId: string;\r\n  status: \"starting\" | \"building\" | \"complete\" | \"error\" | \"cancelled\";\r\n  currentStatus?: {\r\n    target_article_count: number;\r\n    processed_article_count: number;\r\n    progress_percentage: number;\r\n    pending_urls: number;\r\n    processing_urls: number;\r\n    processed_urls: number;\r\n    failed_urls: number;\r\n    article_counts_by_topic: Record<string, number>;\r\n    article_counts_by_industry: Record<string, number>;\r\n    current_plan_title?: string;\r\n    plan_iterations: number;\r\n    last_error?: string;\r\n  };\r\n  finalStatus?: any;\r\n  error?: string;\r\n  message?: string;\r\n  executionTime?: string;\r\n}\r\n\r\nexport function KnowledgeBaseMessageCard({\r\n  className,\r\n  message,\r\n}: {\r\n  className?: string;\r\n  message: Message;\r\n}) {\r\n  const data = useMemo<KnowledgeBaseData>(() => {\r\n    try {\r\n      return JSON.parse(message.content || \"{}\");\r\n    } catch {\r\n      return {} as KnowledgeBaseData;\r\n    }\r\n  }, [message.content]);\r\n\r\n  const isBuilding = useMemo(() => {\r\n    return message.isStreaming || data.status === \"building\" || data.status === \"starting\";\r\n  }, [message.isStreaming, data.status]);\r\n\r\n  const hasError = useMemo(() => {\r\n    return data.status === \"error\" || !!data.error;\r\n  }, [data.status, data.error]);\r\n\r\n  const isComplete = useMemo(() => {\r\n    return data.status === \"complete\";\r\n  }, [data.status]);\r\n\r\n  const isCancelled = useMemo(() => {\r\n    return data.status === \"cancelled\";\r\n  }, [data.status]);\r\n\r\n  const progress = useMemo(() => {\r\n    return data.currentStatus?.progress_percentage || 0;\r\n  }, [data.currentStatus]);\r\n\r\n  const statusIcon = useMemo(() => {\r\n    if (isBuilding) return <LoadingOutlined className=\"animate-spin\" />;\r\n    if (hasError) return <XCircle className=\"text-red-500\" size={16} />;\r\n    if (isCancelled) return <AlertCircle className=\"text-yellow-500\" size={16} />;\r\n    if (isComplete) return <CheckCircle2 className=\"text-green-500\" size={16} />;\r\n    return <Database size={16} />;\r\n  }, [isBuilding, hasError, isCancelled, isComplete]);\r\n\r\n  const statusText = useMemo(() => {\r\n    if (isBuilding) return \"Building knowledge base...\";\r\n    if (hasError) return \"Failed to build knowledge base\";\r\n    if (isCancelled) return \"Knowledge base build cancelled\";\r\n    if (isComplete) return \"Knowledge base built successfully\";\r\n    return \"Knowledge base\";\r\n  }, [isBuilding, hasError, isCancelled, isComplete]);\r\n\r\n  const statusColor = useMemo(() => {\r\n    if (hasError) return \"text-red-500\";\r\n    if (isCancelled) return \"text-yellow-500\";\r\n    if (isComplete) return \"text-green-500\";\r\n    return \"\";\r\n  }, [hasError, isCancelled, isComplete]);\r\n\r\n  return (\r\n    <Card className={cn(\"w-full max-w-[600px]\", className)}>\r\n      <CardHeader>\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\r\n            {statusIcon}\r\n            <RainbowText animated={isBuilding} className={statusColor}>\r\n              {statusText}\r\n            </RainbowText>\r\n          </div>\r\n          {data.currentStatus && isBuilding && (\r\n            <div className=\"text-sm text-muted-foreground\">\r\n              {data.currentStatus.processed_article_count} / {data.currentStatus.target_article_count}\r\n            </div>\r\n          )}\r\n        </div>\r\n        <CardTitle>\r\n          <div className=\"text-lg font-medium\">\r\n            Knowledge Base: {data.request?.topics?.join(\", \")}\r\n          </div>\r\n        </CardTitle>\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"space-y-4\">\r\n        {/* Request Summary */}\r\n        <div className=\"flex flex-wrap gap-2\">\r\n          <Badge variant=\"outline\" className=\"flex items-center gap-1\">\r\n            <FileText size={12} />\r\n            {data.request?.target_article_count} articles\r\n          </Badge>\r\n          {data.request?.industries && data.request.industries.length > 0 && (\r\n            <Badge variant=\"outline\" className=\"flex items-center gap-1\">\r\n              <Globe size={12} />\r\n              {data.request.industries.join(\", \")}\r\n            </Badge>\r\n          )}\r\n          <Badge variant=\"outline\">\r\n            {data.request?.locale || \"en-US\"}\r\n          </Badge>\r\n        </div>\r\n\r\n        {/* Progress Bar */}\r\n        {isBuilding && data.currentStatus && (\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex justify-between text-sm\">\r\n              <span>Progress</span>\r\n              <span>{Math.round(progress)}%</span>\r\n            </div>\r\n            <Progress value={progress} className=\"w-full\" />\r\n            \r\n            {/* Current Plan */}\r\n            {data.currentStatus.current_plan_title && (\r\n              <div className=\"text-sm text-muted-foreground\">\r\n                <Clock size={12} className=\"inline mr-1\" />\r\n                {data.currentStatus.current_plan_title}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* URL Status */}\r\n        {data.currentStatus && (\r\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n            <div className=\"space-y-1\">\r\n              <div className=\"flex justify-between\">\r\n                <span className=\"text-muted-foreground\">Processed:</span>\r\n                <span className=\"font-medium text-green-600\">\r\n                  {data.currentStatus.processed_urls}\r\n                </span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span className=\"text-muted-foreground\">Processing:</span>\r\n                <span className=\"font-medium text-blue-600\">\r\n                  {data.currentStatus.processing_urls}\r\n                </span>\r\n              </div>\r\n            </div>\r\n            <div className=\"space-y-1\">\r\n              <div className=\"flex justify-between\">\r\n                <span className=\"text-muted-foreground\">Pending:</span>\r\n                <span className=\"font-medium text-yellow-600\">\r\n                  {data.currentStatus.pending_urls}\r\n                </span>\r\n              </div>\r\n              <div className=\"flex justify-between\">\r\n                <span className=\"text-muted-foreground\">Failed:</span>\r\n                <span className=\"font-medium text-red-600\">\r\n                  {data.currentStatus.failed_urls}\r\n                </span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Topic Breakdown */}\r\n        {data.currentStatus?.article_counts_by_topic && \r\n         Object.keys(data.currentStatus.article_counts_by_topic).length > 0 && (\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-center gap-1 text-sm text-muted-foreground\">\r\n              <TrendingUp size={12} />\r\n              Articles by Topic\r\n            </div>\r\n            <div className=\"space-y-1\">\r\n              {Object.entries(data.currentStatus.article_counts_by_topic).map(([topic, count]) => (\r\n                <div key={topic} className=\"flex justify-between text-sm\">\r\n                  <span className=\"truncate\">{topic}</span>\r\n                  <span className=\"font-medium\">{count}</span>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Final Results */}\r\n        {isComplete && data.finalStatus && (\r\n          <motion.div \r\n            className=\"rounded-lg bg-green-50 p-3 text-sm\"\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3 }}\r\n          >\r\n            <div className=\"font-medium text-green-800 mb-2\">\r\n              ✅ Successfully acquired {data.finalStatus.processed_article_count} articles\r\n            </div>\r\n            {data.executionTime && (\r\n              <div className=\"text-green-600\">\r\n                Completed in {new Date(data.executionTime).toLocaleTimeString()}\r\n              </div>\r\n            )}\r\n          </motion.div>\r\n        )}\r\n\r\n        {/* Error Display */}\r\n        {hasError && (\r\n          <motion.div \r\n            className=\"rounded-lg bg-red-50 p-3 text-sm\"\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3 }}\r\n          >\r\n            <div className=\"font-medium text-red-800 mb-1\">\r\n              ❌ Build Failed\r\n            </div>\r\n            <div className=\"text-red-600\">\r\n              {data.error || data.currentStatus?.last_error || \"Unknown error occurred\"}\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n\r\n        {/* Cancellation Display */}\r\n        {isCancelled && (\r\n          <motion.div \r\n            className=\"rounded-lg bg-yellow-50 p-3 text-sm\"\r\n            initial={{ opacity: 0, y: 10 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3 }}\r\n          >\r\n            <div className=\"font-medium text-yellow-800\">\r\n              ⚠️ Build was cancelled by user\r\n            </div>\r\n          </motion.div>\r\n        )}\r\n\r\n        {/* Current Message */}\r\n        {data.message && isBuilding && (\r\n          <div className=\"text-sm text-muted-foreground italic\">\r\n            {data.message}\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}"], "names": [], "mappings": "AAAA,kDAAkD;AAClD,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAEA;AACA;AACA;AAMA;AACA;;;AAzBA;;;;;;;;;;AA0DO,SAAS,yBAAyB,EACvC,SAAS,EACT,OAAO,EAIR;;IACC,MAAM,OAAO,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;kDAAqB;YACtC,IAAI;gBACF,OAAO,KAAK,KAAK,CAAC,QAAQ,OAAO,IAAI;YACvC,EAAE,OAAM;gBACN,OAAO,CAAC;YACV;QACF;iDAAG;QAAC,QAAQ,OAAO;KAAC;IAEpB,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;wDAAE;YACzB,OAAO,QAAQ,WAAW,IAAI,KAAK,MAAM,KAAK,cAAc,KAAK,MAAM,KAAK;QAC9E;uDAAG;QAAC,QAAQ,WAAW;QAAE,KAAK,MAAM;KAAC;IAErC,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;sDAAE;YACvB,OAAO,KAAK,MAAM,KAAK,WAAW,CAAC,CAAC,KAAK,KAAK;QAChD;qDAAG;QAAC,KAAK,MAAM;QAAE,KAAK,KAAK;KAAC;IAE5B,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;wDAAE;YACzB,OAAO,KAAK,MAAM,KAAK;QACzB;uDAAG;QAAC,KAAK,MAAM;KAAC;IAEhB,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;yDAAE;YAC1B,OAAO,KAAK,MAAM,KAAK;QACzB;wDAAG;QAAC,KAAK,MAAM;KAAC;IAEhB,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;sDAAE;YACvB,OAAO,KAAK,aAAa,EAAE,uBAAuB;QACpD;qDAAG;QAAC,KAAK,aAAa;KAAC;IAEvB,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;wDAAE;YACzB,IAAI,YAAY,qBAAO,sSAAC,qUAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YAClD,IAAI,UAAU,qBAAO,sSAAC,mSAAA,CAAA,UAAO;gBAAC,WAAU;gBAAe,MAAM;;;;;;YAC7D,IAAI,aAAa,qBAAO,sSAAC,2SAAA,CAAA,cAAW;gBAAC,WAAU;gBAAkB,MAAM;;;;;;YACvE,IAAI,YAAY,qBAAO,sSAAC,4SAAA,CAAA,eAAY;gBAAC,WAAU;gBAAiB,MAAM;;;;;;YACtE,qBAAO,sSAAC,iSAAA,CAAA,WAAQ;gBAAC,MAAM;;;;;;QACzB;uDAAG;QAAC;QAAY;QAAU;QAAa;KAAW;IAElD,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;wDAAE;YACzB,IAAI,YAAY,OAAO;YACvB,IAAI,UAAU,OAAO;YACrB,IAAI,aAAa,OAAO;YACxB,IAAI,YAAY,OAAO;YACvB,OAAO;QACT;uDAAG;QAAC;QAAY;QAAU;QAAa;KAAW;IAElD,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;yDAAE;YAC1B,IAAI,UAAU,OAAO;YACrB,IAAI,aAAa,OAAO;YACxB,IAAI,YAAY,OAAO;YACvB,OAAO;QACT;wDAAG;QAAC;QAAU;QAAa;KAAW;IAEtC,qBACE,sSAAC,mIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;;0BAC1C,sSAAC,mIAAA,CAAA,aAAU;;kCACT,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;;oCACZ;kDACD,sSAAC,wJAAA,CAAA,cAAW;wCAAC,UAAU;wCAAY,WAAW;kDAC3C;;;;;;;;;;;;4BAGJ,KAAK,aAAa,IAAI,4BACrB,sSAAC;gCAAI,WAAU;;oCACZ,KAAK,aAAa,CAAC,uBAAuB;oCAAC;oCAAI,KAAK,aAAa,CAAC,oBAAoB;;;;;;;;;;;;;kCAI7F,sSAAC,mIAAA,CAAA,YAAS;kCACR,cAAA,sSAAC;4BAAI,WAAU;;gCAAsB;gCAClB,KAAK,OAAO,EAAE,QAAQ,KAAK;;;;;;;;;;;;;;;;;;0BAKlD,sSAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;kDACjC,sSAAC,qSAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCACf,KAAK,OAAO,EAAE;oCAAqB;;;;;;;4BAErC,KAAK,OAAO,EAAE,cAAc,KAAK,OAAO,CAAC,UAAU,CAAC,MAAM,GAAG,mBAC5D,sSAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;kDACjC,sSAAC,2RAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;oCACZ,KAAK,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC;;;;;;;0CAGlC,sSAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;0CACZ,KAAK,OAAO,EAAE,UAAU;;;;;;;;;;;;oBAK5B,cAAc,KAAK,aAAa,kBAC/B,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;kDAAK;;;;;;kDACN,sSAAC;;4CAAM,KAAK,KAAK,CAAC;4CAAU;;;;;;;;;;;;;0CAE9B,sSAAC,uIAAA,CAAA,WAAQ;gCAAC,OAAO;gCAAU,WAAU;;;;;;4BAGpC,KAAK,aAAa,CAAC,kBAAkB,kBACpC,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,2RAAA,CAAA,QAAK;wCAAC,MAAM;wCAAI,WAAU;;;;;;oCAC1B,KAAK,aAAa,CAAC,kBAAkB;;;;;;;;;;;;;oBAO7C,KAAK,aAAa,kBACjB,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,sSAAC;gDAAK,WAAU;0DACb,KAAK,aAAa,CAAC,cAAc;;;;;;;;;;;;kDAGtC,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,sSAAC;gDAAK,WAAU;0DACb,KAAK,aAAa,CAAC,eAAe;;;;;;;;;;;;;;;;;;0CAIzC,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,sSAAC;gDAAK,WAAU;0DACb,KAAK,aAAa,CAAC,YAAY;;;;;;;;;;;;kDAGpC,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,sSAAC;gDAAK,WAAU;0DACb,KAAK,aAAa,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;oBAQxC,KAAK,aAAa,EAAE,2BACpB,OAAO,IAAI,CAAC,KAAK,aAAa,CAAC,uBAAuB,EAAE,MAAM,GAAG,mBAChE,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;;kDACb,sSAAC,ySAAA,CAAA,aAAU;wCAAC,MAAM;;;;;;oCAAM;;;;;;;0CAG1B,sSAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,KAAK,aAAa,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBAC7E,sSAAC;wCAAgB,WAAU;;0DACzB,sSAAC;gDAAK,WAAU;0DAAY;;;;;;0DAC5B,sSAAC;gDAAK,WAAU;0DAAe;;;;;;;uCAFvB;;;;;;;;;;;;;;;;oBAUjB,cAAc,KAAK,WAAW,kBAC7B,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,sSAAC;gCAAI,WAAU;;oCAAkC;oCACtB,KAAK,WAAW,CAAC,uBAAuB;oCAAC;;;;;;;4BAEnE,KAAK,aAAa,kBACjB,sSAAC;gCAAI,WAAU;;oCAAiB;oCAChB,IAAI,KAAK,KAAK,aAAa,EAAE,kBAAkB;;;;;;;;;;;;;oBAOpE,0BACC,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,sSAAC;gCAAI,WAAU;0CAAgC;;;;;;0CAG/C,sSAAC;gCAAI,WAAU;0CACZ,KAAK,KAAK,IAAI,KAAK,aAAa,EAAE,cAAc;;;;;;;;;;;;oBAMtD,6BACC,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,sSAAC;4BAAI,WAAU;sCAA8B;;;;;;;;;;;oBAOhD,KAAK,OAAO,IAAI,4BACf,sSAAC;wBAAI,WAAU;kCACZ,KAAK,OAAO;;;;;;;;;;;;;;;;;;AAMzB;GAtOgB;KAAA", "debugId": null}}, {"offset": {"line": 1923, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/chat/components/message-list-view.tsx"], "sourcesContent": ["// web/src/app/chat/components/message-list-view.tsx\r\n// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport { LoadingOutlined } from \"@ant-design/icons\";\r\nimport { motion } from \"framer-motion\";\r\nimport { Download, Headphones } from \"lucide-react\";\r\nimport { useCallback, useMemo, useRef, useState } from \"react\";\r\n\r\nimport { LoadingAnimation } from \"~/components/deer-flow/loading-animation\";\r\nimport { Markdown } from \"~/components/deer-flow/markdown\";\r\nimport { RainbowText } from \"~/components/deer-flow/rainbow-text\";\r\nimport { RollingText } from \"~/components/deer-flow/rolling-text\";\r\nimport {\r\n  ScrollContainer,\r\n  type ScrollContainerRef,\r\n} from \"~/components/deer-flow/scroll-container\";\r\nimport { Tooltip } from \"~/components/deer-flow/tooltip\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport {\r\n  <PERSON>,\r\n  CardContent,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"~/components/ui/card\";\r\nimport type { Message, Option } from \"~/core/messages\";\r\nimport {\r\n  closeResearch,\r\n  openResearch,\r\n  useLastFeedbackMessageId,\r\n  useLastInterruptMessage,\r\n  useMessage,\r\n  useMessageIds,\r\n  useResearchMessage,\r\n  useStore,\r\n} from \"~/core/store\";\r\nimport { parseJSON } from \"~/core/utils\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\n// Import KB components\r\nimport { KnowledgeBaseMessageCard } from \"./kb-message-card\";\r\n\r\nexport function MessageListView({\r\n  className,\r\n  onFeedback,\r\n  onSendMessage,\r\n}: {\r\n  className?: string;\r\n  onFeedback?: (feedback: { option: Option }) => void;\r\n  onSendMessage?: (\r\n    message: string,\r\n    options?: { interruptFeedback?: string },\r\n  ) => void;\r\n}) {\r\n  const scrollContainerRef = useRef<ScrollContainerRef>(null);\r\n  const messageIds = useMessageIds();\r\n  const interruptMessage = useLastInterruptMessage();\r\n  const waitingForFeedbackMessageId = useLastFeedbackMessageId();\r\n  const responding = useStore((state) => state.responding);\r\n  const noOngoingResearch = useStore(\r\n    (state) => state.ongoingResearchId === null,\r\n  );\r\n  const ongoingResearchIsOpen = useStore(\r\n    (state) => state.ongoingResearchId === state.openResearchId,\r\n  );\r\n\r\n  const handleToggleResearch = useCallback(() => {\r\n    // Fix the issue where auto-scrolling to the bottom\r\n    // occasionally fails when toggling research.\r\n    const timer = setTimeout(() => {\r\n      if (scrollContainerRef.current) {\r\n        scrollContainerRef.current.scrollToBottom();\r\n      }\r\n    }, 500);\r\n    return () => {\r\n      clearTimeout(timer);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <ScrollContainer\r\n      className={cn(\"flex h-full w-full flex-col overflow-hidden\", className)}\r\n      scrollShadowColor=\"var(--app-background)\"\r\n      autoScrollToBottom\r\n      ref={scrollContainerRef}\r\n    >\r\n      <ul className=\"flex flex-col\">\r\n        {messageIds.map((messageId) => (\r\n          <MessageListItem\r\n            key={messageId}\r\n            messageId={messageId}\r\n            waitForFeedback={waitingForFeedbackMessageId === messageId}\r\n            interruptMessage={interruptMessage}\r\n            onFeedback={onFeedback}\r\n            onSendMessage={onSendMessage}\r\n            onToggleResearch={handleToggleResearch}\r\n          />\r\n        ))}\r\n        <div className=\"flex h-8 w-full shrink-0\"></div>\r\n      </ul>\r\n      {responding && (noOngoingResearch || !ongoingResearchIsOpen) && (\r\n        <LoadingAnimation className=\"ml-4\" />\r\n      )}\r\n    </ScrollContainer>\r\n  );\r\n}\r\n\r\nfunction MessageListItem({\r\n  className,\r\n  messageId,\r\n  waitForFeedback,\r\n  interruptMessage,\r\n  onFeedback,\r\n  onSendMessage,\r\n  onToggleResearch,\r\n}: {\r\n  className?: string;\r\n  messageId: string;\r\n  waitForFeedback?: boolean;\r\n  onFeedback?: (feedback: { option: Option }) => void;\r\n  interruptMessage?: Message | null;\r\n  onSendMessage?: (\r\n    message: string,\r\n    options?: { interruptFeedback?: string },\r\n  ) => void;\r\n  onToggleResearch?: () => void;\r\n}) {\r\n  const message = useMessage(messageId);\r\n  const researchIds = useStore((state) => state.researchIds);\r\n  const startOfResearch = useMemo(() => {\r\n    return researchIds.includes(messageId);\r\n  }, [researchIds, messageId]);\r\n\r\n  if (message) {\r\n    if (\r\n      message.role === \"user\" ||\r\n      message.agent === \"coordinator\" ||\r\n      message.agent === \"planner\" ||\r\n      message.agent === \"podcast\" ||\r\n      message.agent === \"kb_builder\" ||  // Add KB builder agent\r\n      startOfResearch\r\n    ) {\r\n      let content: React.ReactNode;\r\n      if (message.agent === \"planner\") {\r\n        content = (\r\n          <div className=\"w-full px-4\">\r\n            <PlanCard\r\n              message={message}\r\n              waitForFeedback={waitForFeedback}\r\n              interruptMessage={interruptMessage}\r\n              onFeedback={onFeedback}\r\n              onSendMessage={onSendMessage}\r\n            />\r\n          </div>\r\n        );\r\n      } else if (message.agent === \"podcast\") {\r\n        content = (\r\n          <div className=\"w-full px-4\">\r\n            <PodcastCard message={message} />\r\n          </div>\r\n        );\r\n      } else if (message.agent === \"kb_builder\") {\r\n        // Add KB builder message handling\r\n        content = (\r\n          <div className=\"w-full px-4\">\r\n            <KnowledgeBaseMessageCard message={message} />\r\n          </div>\r\n        );\r\n      } else if (startOfResearch) {\r\n        content = (\r\n          <div className=\"w-full px-4\">\r\n            <ResearchCard\r\n              researchId={message.id}\r\n              onToggleResearch={onToggleResearch}\r\n            />\r\n          </div>\r\n        );\r\n      } else {\r\n        content = message.content ? (\r\n          <div\r\n            className={cn(\r\n              \"flex w-full px-4\",\r\n              message.role === \"user\" && \"justify-end\",\r\n              className,\r\n            )}\r\n          >\r\n            <MessageBubble message={message}>\r\n              <div className=\"flex w-full flex-col\">\r\n                <Markdown>{message?.content}</Markdown>\r\n              </div>\r\n            </MessageBubble>\r\n          </div>\r\n        ) : null;\r\n      }\r\n      if (content) {\r\n        return (\r\n          <motion.li\r\n            className=\"mt-10\"\r\n            key={messageId}\r\n            initial={{ opacity: 0, y: 24 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            style={{ transition: \"all 0.2s ease-out\" }}\r\n            transition={{\r\n              duration: 0.2,\r\n              ease: \"easeOut\",\r\n            }}\r\n          >\r\n            {content}\r\n          </motion.li>\r\n        );\r\n      }\r\n    }\r\n    return null;\r\n  }\r\n}\r\n\r\nfunction MessageBubble({\r\n  className,\r\n  message,\r\n  children,\r\n}: {\r\n  className?: string;\r\n  message: Message;\r\n  children: React.ReactNode;\r\n}) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        `flex w-fit max-w-[85%] flex-col rounded-2xl px-4 py-3 shadow`,\r\n        message.role === \"user\" &&\r\n          \"text-primary-foreground bg-brand rounded-ee-none\",\r\n        message.role === \"assistant\" && \"bg-card rounded-es-none\",\r\n        className,\r\n      )}\r\n    >\r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction ResearchCard({\r\n  className,\r\n  researchId,\r\n  onToggleResearch,\r\n}: {\r\n  className?: string;\r\n  researchId: string;\r\n  onToggleResearch?: () => void;\r\n}) {\r\n  const reportId = useStore((state) => state.researchReportIds.get(researchId));\r\n  const hasReport = reportId !== undefined;\r\n  const reportGenerating = useStore(\r\n    (state) => hasReport && state.messages.get(reportId)!.isStreaming,\r\n  );\r\n  const openResearchId = useStore((state) => state.openResearchId);\r\n  const state = useMemo(() => {\r\n    if (hasReport) {\r\n      return reportGenerating ? \"Generating report...\" : \"Report generated\";\r\n    }\r\n    return \"Researching...\";\r\n  }, [hasReport, reportGenerating]);\r\n  const msg = useResearchMessage(researchId);\r\n  const title = useMemo(() => {\r\n    if (msg) {\r\n      return parseJSON(msg.content ?? \"\", { title: \"\" }).title;\r\n    }\r\n    return undefined;\r\n  }, [msg]);\r\n  const handleOpen = useCallback(() => {\r\n    if (openResearchId === researchId) {\r\n      closeResearch();\r\n    } else {\r\n      openResearch(researchId);\r\n    }\r\n    onToggleResearch?.();\r\n  }, [openResearchId, researchId, onToggleResearch]);\r\n  return (\r\n    <Card className={cn(\"w-full\", className)}>\r\n      <CardHeader>\r\n        <CardTitle>\r\n          <RainbowText animated={state !== \"Report generated\"}>\r\n            {title !== undefined && title !== \"\" ? title : \"Deep Research\"}\r\n          </RainbowText>\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardFooter>\r\n        <div className=\"flex w-full\">\r\n          <RollingText className=\"text-muted-foreground flex-grow text-sm\">\r\n            {state}\r\n          </RollingText>\r\n          <Button\r\n            variant={!openResearchId ? \"default\" : \"outline\"}\r\n            onClick={handleOpen}\r\n          >\r\n            {researchId !== openResearchId ? \"Open\" : \"Close\"}\r\n          </Button>\r\n        </div>\r\n      </CardFooter>\r\n    </Card>\r\n  );\r\n}\r\n\r\nconst GREETINGS = [\"Cool\", \"Sounds great\", \"Looks good\", \"Great\", \"Awesome\"];\r\nfunction PlanCard({\r\n  className,\r\n  message,\r\n  interruptMessage,\r\n  onFeedback,\r\n  waitForFeedback,\r\n  onSendMessage,\r\n}: {\r\n  className?: string;\r\n  message: Message;\r\n  interruptMessage?: Message | null;\r\n  onFeedback?: (feedback: { option: Option }) => void;\r\n  onSendMessage?: (\r\n    message: string,\r\n    options?: { interruptFeedback?: string },\r\n  ) => void;\r\n  waitForFeedback?: boolean;\r\n}) {\r\n  const plan = useMemo<{\r\n    title?: string;\r\n    thought?: string;\r\n    steps?: { title?: string; description?: string }[];\r\n  }>(() => {\r\n    return parseJSON(message.content ?? \"\", {});\r\n  }, [message.content]);\r\n  const handleAccept = useCallback(async () => {\r\n    if (onSendMessage) {\r\n      onSendMessage(\r\n        `${GREETINGS[Math.floor(Math.random() * GREETINGS.length)]}! ${Math.random() > 0.5 ? \"Let's get started.\" : \"Let's start.\"}`,\r\n        {\r\n          interruptFeedback: \"accepted\",\r\n        },\r\n      );\r\n    }\r\n  }, [onSendMessage]);\r\n  return (\r\n    <Card className={cn(\"w-full\", className)}>\r\n      <CardHeader>\r\n        <CardTitle>\r\n          <Markdown animated>\r\n            {`### ${\r\n              plan.title !== undefined && plan.title !== \"\"\r\n                ? plan.title\r\n                : \"Deep Research\"\r\n            }`}\r\n          </Markdown>\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent>\r\n        <Markdown className=\"opacity-80\" animated>\r\n          {plan.thought}\r\n        </Markdown>\r\n        {plan.steps && (\r\n          <ul className=\"my-2 flex list-decimal flex-col gap-4 border-l-[2px] pl-8\">\r\n            {plan.steps.map((step, i) => (\r\n              <li key={`step-${i}`}>\r\n                <h3 className=\"mb text-lg font-medium\">\r\n                  <Markdown animated>{step.title}</Markdown>\r\n                </h3>\r\n                <div className=\"text-muted-foreground text-sm\">\r\n                  <Markdown animated>{step.description}</Markdown>\r\n                </div>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        )}\r\n      </CardContent>\r\n      <CardFooter className=\"flex justify-end\">\r\n        {!message.isStreaming && interruptMessage?.options?.length && (\r\n          <motion.div\r\n            className=\"flex gap-2\"\r\n            initial={{ opacity: 0, y: 12 }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.3, delay: 0.3 }}\r\n          >\r\n            {interruptMessage?.options.map((option) => (\r\n              <Button\r\n                key={option.value}\r\n                variant={option.value === \"accepted\" ? \"default\" : \"outline\"}\r\n                disabled={!waitForFeedback}\r\n                onClick={() => {\r\n                  if (option.value === \"accepted\") {\r\n                    void handleAccept();\r\n                  } else {\r\n                    onFeedback?.({\r\n                      option,\r\n                    });\r\n                  }\r\n                }}\r\n              >\r\n                {option.text}\r\n              </Button>\r\n            ))}\r\n          </motion.div>\r\n        )}\r\n      </CardFooter>\r\n    </Card>\r\n  );\r\n}\r\n\r\nfunction PodcastCard({\r\n  className,\r\n  message,\r\n}: {\r\n  className?: string;\r\n  message: Message;\r\n}) {\r\n  const data = useMemo(() => {\r\n    return JSON.parse(message.content ?? \"\");\r\n  }, [message.content]);\r\n  const title = useMemo<string | undefined>(() => data?.title, [data]);\r\n  const audioUrl = useMemo<string | undefined>(() => data?.audioUrl, [data]);\r\n  const isGenerating = useMemo(() => {\r\n    return message.isStreaming;\r\n  }, [message.isStreaming]);\r\n  const hasError = useMemo(() => {\r\n    return data?.error !== undefined;\r\n  }, [data]);\r\n  const [isPlaying, setIsPlaying] = useState(false);\r\n  return (\r\n    <Card className={cn(\"w-[508px]\", className)}>\r\n      <CardHeader>\r\n        <div className=\"text-muted-foreground flex items-center justify-between text-sm\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {isGenerating ? <LoadingOutlined /> : <Headphones size={16} />}\r\n            {!hasError ? (\r\n              <RainbowText animated={isGenerating}>\r\n                {isGenerating\r\n                  ? \"Generating podcast...\"\r\n                  : isPlaying\r\n                    ? \"Now playing podcast...\"\r\n                    : \"Podcast\"}\r\n              </RainbowText>\r\n            ) : (\r\n              <div className=\"text-red-500\">\r\n                Error when generating podcast. Please try again.\r\n              </div>\r\n            )}\r\n          </div>\r\n          {!hasError && !isGenerating && (\r\n            <div className=\"flex\">\r\n              <Tooltip title=\"Download podcast\">\r\n                <Button variant=\"ghost\" size=\"icon\" asChild>\r\n                  <a\r\n                    href={audioUrl}\r\n                    download={`${(title ?? \"podcast\").replaceAll(\" \", \"-\")}.mp3`}\r\n                  >\r\n                    <Download size={16} />\r\n                  </a>\r\n                </Button>\r\n              </Tooltip>\r\n            </div>\r\n          )}\r\n        </div>\r\n        <CardTitle>\r\n          <div className=\"text-lg font-medium\">\r\n            <RainbowText animated={isGenerating}>{title}</RainbowText>\r\n          </div>\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent>\r\n        {audioUrl ? (\r\n          <audio\r\n            className=\"w-full\"\r\n            src={audioUrl}\r\n            controls\r\n            onPlay={() => setIsPlaying(true)}\r\n            onPause={() => setIsPlaying(false)}\r\n          />\r\n        ) : (\r\n          <div className=\"w-full\"></div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}"], "names": [], "mappings": "AAAA,oDAAoD;AACpD,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AAIA;AACA;AACA;AAQA;AAAA;AAUA;AAAA;AACA;AAEA,uBAAuB;AACvB;;;AAvCA;;;;;;;;;;;;;;;;;AAyCO,SAAS,gBAAgB,EAC9B,SAAS,EACT,UAAU,EACV,aAAa,EAQd;;IACC,MAAM,qBAAqB,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAsB;IACtD,MAAM,aAAa,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,mBAAmB,CAAA,GAAA,gIAAA,CAAA,0BAAuB,AAAD;IAC/C,MAAM,8BAA8B,CAAA,GAAA,gIAAA,CAAA,2BAAwB,AAAD;IAC3D,MAAM,aAAa,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;gDAAE,CAAC,QAAU,MAAM,UAAU;;IACvD,MAAM,oBAAoB,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;uDAC/B,CAAC,QAAU,MAAM,iBAAiB,KAAK;;IAEzC,MAAM,wBAAwB,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;2DACnC,CAAC,QAAU,MAAM,iBAAiB,KAAK,MAAM,cAAc;;IAG7D,MAAM,uBAAuB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;6DAAE;YACvC,mDAAmD;YACnD,6CAA6C;YAC7C,MAAM,QAAQ;2EAAW;oBACvB,IAAI,mBAAmB,OAAO,EAAE;wBAC9B,mBAAmB,OAAO,CAAC,cAAc;oBAC3C;gBACF;0EAAG;YACH;qEAAO;oBACL,aAAa;gBACf;;QACF;4DAAG,EAAE;IAEL,qBACE,sSAAC,4JAAA,CAAA,kBAAe;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC7D,mBAAkB;QAClB,kBAAkB;QAClB,KAAK;;0BAEL,sSAAC;gBAAG,WAAU;;oBACX,WAAW,GAAG,CAAC,CAAC,0BACf,sSAAC;4BAEC,WAAW;4BACX,iBAAiB,gCAAgC;4BACjD,kBAAkB;4BAClB,YAAY;4BACZ,eAAe;4BACf,kBAAkB;2BANb;;;;;kCAST,sSAAC;wBAAI,WAAU;;;;;;;;;;;;YAEhB,cAAc,CAAC,qBAAqB,CAAC,qBAAqB,mBACzD,sSAAC,6JAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAIpC;GA/DgB;;QAaK,gIAAA,CAAA,gBAAa;QACP,gIAAA,CAAA,0BAAuB;QACZ,gIAAA,CAAA,2BAAwB;QACzC,gIAAA,CAAA,WAAQ;QACD,gIAAA,CAAA,WAAQ;QAGJ,gIAAA,CAAA,WAAQ;;;KApBxB;AAiEhB,SAAS,gBAAgB,EACvB,SAAS,EACT,SAAS,EACT,eAAe,EACf,gBAAgB,EAChB,UAAU,EACV,aAAa,EACb,gBAAgB,EAYjB;;IACC,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;iDAAE,CAAC,QAAU,MAAM,WAAW;;IACzD,MAAM,kBAAkB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;oDAAE;YAC9B,OAAO,YAAY,QAAQ,CAAC;QAC9B;mDAAG;QAAC;QAAa;KAAU;IAE3B,IAAI,SAAS;QACX,IACE,QAAQ,IAAI,KAAK,UACjB,QAAQ,KAAK,KAAK,iBAClB,QAAQ,KAAK,KAAK,aAClB,QAAQ,KAAK,KAAK,aAClB,QAAQ,KAAK,KAAK,gBAAiB,uBAAuB;QAC1D,iBACA;YACA,IAAI;YACJ,IAAI,QAAQ,KAAK,KAAK,WAAW;gBAC/B,wBACE,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;wBACC,SAAS;wBACT,iBAAiB;wBACjB,kBAAkB;wBAClB,YAAY;wBACZ,eAAe;;;;;;;;;;;YAIvB,OAAO,IAAI,QAAQ,KAAK,KAAK,WAAW;gBACtC,wBACE,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;wBAAY,SAAS;;;;;;;;;;;YAG5B,OAAO,IAAI,QAAQ,KAAK,KAAK,cAAc;gBACzC,kCAAkC;gBAClC,wBACE,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC,6JAAA,CAAA,2BAAwB;wBAAC,SAAS;;;;;;;;;;;YAGzC,OAAO,IAAI,iBAAiB;gBAC1B,wBACE,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC;wBACC,YAAY,QAAQ,EAAE;wBACtB,kBAAkB;;;;;;;;;;;YAI1B,OAAO;gBACL,UAAU,QAAQ,OAAO,iBACvB,sSAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oBACA,QAAQ,IAAI,KAAK,UAAU,eAC3B;8BAGF,cAAA,sSAAC;wBAAc,SAAS;kCACtB,cAAA,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,iJAAA,CAAA,WAAQ;0CAAE,SAAS;;;;;;;;;;;;;;;;;;;;2BAIxB;YACN;YACA,IAAI,SAAS;gBACX,qBACE,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;oBACR,WAAU;oBAEV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,OAAO;wBAAE,YAAY;oBAAoB;oBACzC,YAAY;wBACV,UAAU;wBACV,MAAM;oBACR;8BAEC;mBATI;;;;;YAYX;QACF;QACA,OAAO;IACT;AACF;IA3GS;;QAoBS,gIAAA,CAAA,aAAU;QACN,gIAAA,CAAA,WAAQ;;;MArBrB;AA6GT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,EACP,QAAQ,EAKT;IACC,qBACE,sSAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAC,4DAA4D,CAAC,EAC9D,QAAQ,IAAI,KAAK,UACf,oDACF,QAAQ,IAAI,KAAK,eAAe,2BAChC;kBAGD;;;;;;AAGP;MAtBS;AAwBT,SAAS,aAAa,EACpB,SAAS,EACT,UAAU,EACV,gBAAgB,EAKjB;;IACC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;2CAAE,CAAC,QAAU,MAAM,iBAAiB,CAAC,GAAG,CAAC;;IACjE,MAAM,YAAY,aAAa;IAC/B,MAAM,mBAAmB,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;mDAC9B,CAAC,QAAU,aAAa,MAAM,QAAQ,CAAC,GAAG,CAAC,UAAW,WAAW;;IAEnE,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;iDAAE,CAAC,QAAU,MAAM,cAAc;;IAC/D,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;uCAAE;YACpB,IAAI,WAAW;gBACb,OAAO,mBAAmB,yBAAyB;YACrD;YACA,OAAO;QACT;sCAAG;QAAC;QAAW;KAAiB;IAChC,MAAM,MAAM,CAAA,GAAA,gIAAA,CAAA,qBAAkB,AAAD,EAAE;IAC/B,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;uCAAE;YACpB,IAAI,KAAK;gBACP,OAAO,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD,EAAE,IAAI,OAAO,IAAI,IAAI;oBAAE,OAAO;gBAAG,GAAG,KAAK;YAC1D;YACA,OAAO;QACT;sCAAG;QAAC;KAAI;IACR,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;gDAAE;YAC7B,IAAI,mBAAmB,YAAY;gBACjC,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;YACd,OAAO;gBACL,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD,EAAE;YACf;YACA;QACF;+CAAG;QAAC;QAAgB;QAAY;KAAiB;IACjD,qBACE,sSAAC,mIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;0BAC5B,sSAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,sSAAC,mIAAA,CAAA,YAAS;8BACR,cAAA,sSAAC,wJAAA,CAAA,cAAW;wBAAC,UAAU,UAAU;kCAC9B,UAAU,aAAa,UAAU,KAAK,QAAQ;;;;;;;;;;;;;;;;0BAIrD,sSAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,sSAAC;oBAAI,WAAU;;sCACb,sSAAC,wJAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB;;;;;;sCAEH,sSAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,CAAC,iBAAiB,YAAY;4BACvC,SAAS;sCAER,eAAe,iBAAiB,SAAS;;;;;;;;;;;;;;;;;;;;;;;AAMtD;IA5DS;;QASU,gIAAA,CAAA,WAAQ;QAEA,gIAAA,CAAA,WAAQ;QAGV,gIAAA,CAAA,WAAQ;QAOnB,gIAAA,CAAA,qBAAkB;;;MArBvB;AA8DT,MAAM,YAAY;IAAC;IAAQ;IAAgB;IAAc;IAAS;CAAU;AAC5E,SAAS,SAAS,EAChB,SAAS,EACT,OAAO,EACP,gBAAgB,EAChB,UAAU,EACV,eAAe,EACf,aAAa,EAWd;;IACC,MAAM,OAAO,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;kCAIhB;YACD,OAAO,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,OAAO,IAAI,IAAI,CAAC;QAC3C;iCAAG;QAAC,QAAQ,OAAO;KAAC;IACpB,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;8CAAE;YAC/B,IAAI,eAAe;gBACjB,cACE,GAAG,SAAS,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,MAAM,EAAE,CAAC,EAAE,EAAE,KAAK,MAAM,KAAK,MAAM,uBAAuB,gBAAgB,EAC5H;oBACE,mBAAmB;gBACrB;YAEJ;QACF;6CAAG;QAAC;KAAc;IAClB,qBACE,sSAAC,mIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;0BAC5B,sSAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,sSAAC,mIAAA,CAAA,YAAS;8BACR,cAAA,sSAAC,iJAAA,CAAA,WAAQ;wBAAC,QAAQ;kCACf,CAAC,IAAI,EACJ,KAAK,KAAK,KAAK,aAAa,KAAK,KAAK,KAAK,KACvC,KAAK,KAAK,GACV,iBACJ;;;;;;;;;;;;;;;;0BAIR,sSAAC,mIAAA,CAAA,cAAW;;kCACV,sSAAC,iJAAA,CAAA,WAAQ;wBAAC,WAAU;wBAAa,QAAQ;kCACtC,KAAK,OAAO;;;;;;oBAEd,KAAK,KAAK,kBACT,sSAAC;wBAAG,WAAU;kCACX,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,kBACrB,sSAAC;;kDACC,sSAAC;wCAAG,WAAU;kDACZ,cAAA,sSAAC,iJAAA,CAAA,WAAQ;4CAAC,QAAQ;sDAAE,KAAK,KAAK;;;;;;;;;;;kDAEhC,sSAAC;wCAAI,WAAU;kDACb,cAAA,sSAAC,iJAAA,CAAA,WAAQ;4CAAC,QAAQ;sDAAE,KAAK,WAAW;;;;;;;;;;;;+BAL/B,CAAC,KAAK,EAAE,GAAG;;;;;;;;;;;;;;;;0BAY5B,sSAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACnB,CAAC,QAAQ,WAAW,IAAI,kBAAkB,SAAS,wBAClD,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;8BAEvC,kBAAkB,QAAQ,IAAI,CAAC,uBAC9B,sSAAC,qIAAA,CAAA,SAAM;4BAEL,SAAS,OAAO,KAAK,KAAK,aAAa,YAAY;4BACnD,UAAU,CAAC;4BACX,SAAS;gCACP,IAAI,OAAO,KAAK,KAAK,YAAY;oCAC/B,KAAK;gCACP,OAAO;oCACL,aAAa;wCACX;oCACF;gCACF;4BACF;sCAEC,OAAO,IAAI;2BAbP,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;AAqBjC;IAlGS;MAAA;AAoGT,SAAS,YAAY,EACnB,SAAS,EACT,OAAO,EAIR;;IACC,MAAM,OAAO,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;qCAAE;YACnB,OAAO,KAAK,KAAK,CAAC,QAAQ,OAAO,IAAI;QACvC;oCAAG;QAAC,QAAQ,OAAO;KAAC;IACpB,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;sCAAsB,IAAM,MAAM;qCAAO;QAAC;KAAK;IACnE,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;yCAAsB,IAAM,MAAM;wCAAU;QAAC;KAAK;IACzE,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;6CAAE;YAC3B,OAAO,QAAQ,WAAW;QAC5B;4CAAG;QAAC,QAAQ,WAAW;KAAC;IACxB,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;yCAAE;YACvB,OAAO,MAAM,UAAU;QACzB;wCAAG;QAAC;KAAK;IACT,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,qBACE,sSAAC,mIAAA,CAAA,OAAI;QAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;;0BAC/B,sSAAC,mIAAA,CAAA,aAAU;;kCACT,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;;oCACZ,6BAAe,sSAAC,qUAAA,CAAA,kBAAe;;;;6DAAM,sSAAC,qSAAA,CAAA,aAAU;wCAAC,MAAM;;;;;;oCACvD,CAAC,yBACA,sSAAC,wJAAA,CAAA,cAAW;wCAAC,UAAU;kDACpB,eACG,0BACA,YACE,2BACA;;;;;6DAGR,sSAAC;wCAAI,WAAU;kDAAe;;;;;;;;;;;;4BAKjC,CAAC,YAAY,CAAC,8BACb,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC,gJAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAO,OAAO;kDACzC,cAAA,sSAAC;4CACC,MAAM;4CACN,UAAU,GAAG,CAAC,SAAS,SAAS,EAAE,UAAU,CAAC,KAAK,KAAK,IAAI,CAAC;sDAE5D,cAAA,sSAAC,iSAAA,CAAA,WAAQ;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO5B,sSAAC,mIAAA,CAAA,YAAS;kCACR,cAAA,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,wJAAA,CAAA,cAAW;gCAAC,UAAU;0CAAe;;;;;;;;;;;;;;;;;;;;;;0BAI5C,sSAAC,mIAAA,CAAA,cAAW;0BACT,yBACC,sSAAC;oBACC,WAAU;oBACV,KAAK;oBACL,QAAQ;oBACR,QAAQ,IAAM,aAAa;oBAC3B,SAAS,IAAM,aAAa;;;;;yCAG9B,sSAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAKzB;IA3ES;MAAA", "debugId": null}}, {"offset": {"line": 2704, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/chat/components/messages-block.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { motion } from \"framer-motion\";\r\nimport { FastForward, Play } from \"lucide-react\";\r\nimport { useCallback, useRef, useState } from \"react\";\r\n\r\nimport { RainbowText } from \"~/components/deer-flow/rainbow-text\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport {\r\n  Card,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"~/components/ui/card\";\r\nimport { fastForwardReplay } from \"~/core/api\";\r\nimport { useReplayMetadata } from \"~/core/api/hooks\";\r\nimport type { Option } from \"~/core/messages\";\r\nimport { useReplay } from \"~/core/replay\";\r\nimport { sendMessage, useMessageIds, useStore } from \"~/core/store\";\r\nimport { env } from \"~/env\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport { ConversationStarter } from \"./conversation-starter\";\r\nimport { InputBox } from \"./input-box\";\r\nimport { MessageListView } from \"./message-list-view\";\r\nimport { Welcome } from \"./welcome\";\r\nexport function MessagesBlock({ className }: { className?: string }) {\r\n  const messageIds = useMessageIds();\r\n  const messageCount = messageIds.length;\r\n  const responding = useStore((state) => state.responding);\r\n  const { isReplay } = useReplay();\r\n  const { title: replayTitle, hasError: replayHasError } = useReplayMetadata();\r\n  const [replayStarted, setReplayStarted] = useState(false);\r\n  const abortControllerRef = useRef<AbortController | null>(null);\r\n  const [feedback, setFeedback] = useState<{ option: Option } | null>(null);\r\n  const handleSend = useCallback(\r\n    async (message: string, options?: { interruptFeedback?: string }) => {\r\n      const abortController = new AbortController();\r\n      abortControllerRef.current = abortController;\r\n      try {\r\n        await sendMessage(\r\n          message,\r\n          {\r\n            interruptFeedback:\r\n              options?.interruptFeedback ?? feedback?.option.value,\r\n          },\r\n          {\r\n            abortSignal: abortController.signal,\r\n          },\r\n        );\r\n      } catch {}\r\n    },\r\n    [feedback],\r\n  );\r\n  const handleCancel = useCallback(() => {\r\n    abortControllerRef.current?.abort();\r\n    abortControllerRef.current = null;\r\n  }, []);\r\n  const handleFeedback = useCallback(\r\n    (feedback: { option: Option }) => {\r\n      setFeedback(feedback);\r\n    },\r\n    [setFeedback],\r\n  );\r\n  const handleRemoveFeedback = useCallback(() => {\r\n    setFeedback(null);\r\n  }, [setFeedback]);\r\n  const handleStartReplay = useCallback(() => {\r\n    setReplayStarted(true);\r\n    void sendMessage();\r\n  }, [setReplayStarted]);\r\n  const [fastForwarding, setFastForwarding] = useState(false);\r\n  const handleFastForwardReplay = useCallback(() => {\r\n    setFastForwarding(!fastForwarding);\r\n    fastForwardReplay(!fastForwarding);\r\n  }, [fastForwarding]);\r\n  return (\r\n    <div className={cn(\"flex h-full flex-col\", className)}>\r\n      <MessageListView\r\n        className=\"flex flex-grow\"\r\n        onFeedback={handleFeedback}\r\n        onSendMessage={handleSend}\r\n      />\r\n      {!isReplay ? (\r\n        <div className=\"relative flex h-42 shrink-0 pb-4\">\r\n          {!responding && messageCount === 0 && (\r\n            <ConversationStarter\r\n              className=\"absolute top-[-218px] left-0\"\r\n              onSend={handleSend}\r\n            />\r\n          )}\r\n          <InputBox\r\n            className=\"h-full w-full\"\r\n            responding={responding}\r\n            feedback={feedback}\r\n            onSend={handleSend}\r\n            onCancel={handleCancel}\r\n            onRemoveFeedback={handleRemoveFeedback}\r\n          />\r\n        </div>\r\n      ) : (\r\n        <>\r\n          <div\r\n            className={cn(\r\n              \"fixed bottom-[calc(50vh+80px)] left-0 transition-all duration-500 ease-out\",\r\n              replayStarted && \"pointer-events-none scale-150 opacity-0\",\r\n            )}\r\n          >\r\n            <Welcome />\r\n          </div>\r\n          <motion.div\r\n            className=\"mb-4 h-fit w-full items-center justify-center\"\r\n            initial={{ opacity: 0, y: \"20vh\" }}\r\n            animate={{ opacity: 1, y: 0 }}\r\n            transition={{ duration: 0.5 }}\r\n          >\r\n            <Card\r\n              className={cn(\r\n                \"w-full transition-all duration-300\",\r\n                !replayStarted && \"translate-y-[-40vh]\",\r\n              )}\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex-grow\">\r\n                  <CardHeader>\r\n                    <CardTitle>\r\n                      <RainbowText animated={responding}>\r\n                        {responding ? \"Replaying\" : `${replayTitle}`}\r\n                      </RainbowText>\r\n                    </CardTitle>\r\n                    <CardDescription>\r\n                      <RainbowText animated={responding}>\r\n                        {responding\r\n                          ? \"DeerFlow is now replaying the conversation...\"\r\n                          : replayStarted\r\n                            ? \"The replay has been stopped.\"\r\n                            : `You're now in DeerFlow's replay mode. Click the \"Play\" button on the right to start.`}\r\n                      </RainbowText>\r\n                    </CardDescription>\r\n                  </CardHeader>\r\n                </div>\r\n                {!replayHasError && (\r\n                  <div className=\"pr-4\">\r\n                    {responding && (\r\n                      <Button\r\n                        className={cn(fastForwarding && \"animate-pulse\")}\r\n                        variant={fastForwarding ? \"default\" : \"outline\"}\r\n                        onClick={handleFastForwardReplay}\r\n                      >\r\n                        <FastForward size={16} />\r\n                        Fast Forward\r\n                      </Button>\r\n                    )}\r\n                    {!replayStarted && (\r\n                      <Button className=\"w-24\" onClick={handleStartReplay}>\r\n                        <Play size={16} />\r\n                        Play\r\n                      </Button>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </Card>\r\n            {!replayStarted && env.NEXT_PUBLIC_STATIC_WEBSITE_ONLY && (\r\n              <div className=\"text-muted-foreground w-full text-center text-xs\">\r\n                * This site is for demo purposes only. If you want to try your\r\n                own question, please{\" \"}\r\n                <a\r\n                  className=\"underline\"\r\n                  href=\"https://github.com/bytedance/deer-flow\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                >\r\n                  click here\r\n                </a>{\" \"}\r\n                to clone it locally and run it.\r\n              </div>\r\n            )}\r\n          </motion.div>\r\n        </>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AAAA;AACA;AAEA;AACA;AACA;AAMA;AAAA;AACA;AAEA;AAAA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AACO,SAAS,cAAc,EAAE,SAAS,EAA0B;;IACjE,MAAM,aAAa,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,eAAe,WAAW,MAAM;IACtC,MAAM,aAAa,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;8CAAE,CAAC,QAAU,MAAM,UAAU;;IACvD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,EAAE,OAAO,WAAW,EAAE,UAAU,cAAc,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;IACzE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,qBAAqB,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAA0B;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAA6B;IACpE,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;iDAC3B,OAAO,SAAiB;YACtB,MAAM,kBAAkB,IAAI;YAC5B,mBAAmB,OAAO,GAAG;YAC7B,IAAI;gBACF,MAAM,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD,EACd,SACA;oBACE,mBACE,SAAS,qBAAqB,UAAU,OAAO;gBACnD,GACA;oBACE,aAAa,gBAAgB,MAAM;gBACrC;YAEJ,EAAE,OAAM,CAAC;QACX;gDACA;QAAC;KAAS;IAEZ,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;mDAAE;YAC/B,mBAAmB,OAAO,EAAE;YAC5B,mBAAmB,OAAO,GAAG;QAC/B;kDAAG,EAAE;IACL,MAAM,iBAAiB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;qDAC/B,CAAC;YACC,YAAY;QACd;oDACA;QAAC;KAAY;IAEf,MAAM,uBAAuB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;2DAAE;YACvC,YAAY;QACd;0DAAG;QAAC;KAAY;IAChB,MAAM,oBAAoB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;wDAAE;YACpC,iBAAiB;YACjB,KAAK,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;QACjB;uDAAG;QAAC;KAAiB;IACrB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,0BAA0B,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;8DAAE;YAC1C,kBAAkB,CAAC;YACnB,CAAA,GAAA,6HAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC;QACrB;6DAAG;QAAC;KAAe;IACnB,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;;0BACzC,sSAAC,+JAAA,CAAA,kBAAe;gBACd,WAAU;gBACV,YAAY;gBACZ,eAAe;;;;;;YAEhB,CAAC,yBACA,sSAAC;gBAAI,WAAU;;oBACZ,CAAC,cAAc,iBAAiB,mBAC/B,sSAAC,+JAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,QAAQ;;;;;;kCAGZ,sSAAC,oJAAA,CAAA,WAAQ;wBACP,WAAU;wBACV,YAAY;wBACZ,UAAU;wBACV,QAAQ;wBACR,UAAU;wBACV,kBAAkB;;;;;;;;;;;qCAItB;;kCACE,sSAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8EACA,iBAAiB;kCAGnB,cAAA,sSAAC,+IAAA,CAAA,UAAO;;;;;;;;;;kCAEV,sSAAC,sSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAO;wBACjC,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,sSAAC,mIAAA,CAAA,OAAI;gCACH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sCACA,CAAC,iBAAiB;0CAGpB,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC;4CAAI,WAAU;sDACb,cAAA,sSAAC,mIAAA,CAAA,aAAU;;kEACT,sSAAC,mIAAA,CAAA,YAAS;kEACR,cAAA,sSAAC,wJAAA,CAAA,cAAW;4DAAC,UAAU;sEACpB,aAAa,cAAc,GAAG,aAAa;;;;;;;;;;;kEAGhD,sSAAC,mIAAA,CAAA,kBAAe;kEACd,cAAA,sSAAC,wJAAA,CAAA,cAAW;4DAAC,UAAU;sEACpB,aACG,kDACA,gBACE,iCACA,CAAC,oFAAoF,CAAC;;;;;;;;;;;;;;;;;;;;;;wCAKnG,CAAC,gCACA,sSAAC;4CAAI,WAAU;;gDACZ,4BACC,sSAAC,qIAAA,CAAA,SAAM;oDACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;oDAChC,SAAS,iBAAiB,YAAY;oDACtC,SAAS;;sEAET,sSAAC,2SAAA,CAAA,cAAW;4DAAC,MAAM;;;;;;wDAAM;;;;;;;gDAI5B,CAAC,+BACA,sSAAC,qIAAA,CAAA,SAAM;oDAAC,WAAU;oDAAO,SAAS;;sEAChC,sSAAC,yRAAA,CAAA,OAAI;4DAAC,MAAM;;;;;;wDAAM;;;;;;;;;;;;;;;;;;;;;;;;4BAQ7B,CAAC,iBAAiB,6GAAA,CAAA,MAAG,CAAC,+BAA+B,kBACpD,sSAAC;gCAAI,WAAU;;oCAAmD;oCAE3C;kDACrB,sSAAC;wCACC,WAAU;wCACV,MAAK;wCACL,QAAO;wCACP,KAAI;kDACL;;;;;;oCAEI;oCAAI;;;;;;;;;;;;;;;;;;;;;AASzB;GA7JgB;;QACK,gIAAA,CAAA,gBAAa;QAEb,gIAAA,CAAA,WAAQ;QACN,iIAAA,CAAA,YAAS;QAC2B,8HAAA,CAAA,oBAAiB;;;KAL5D", "debugId": null}}, {"offset": {"line": 3044, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/deer-flow/fav-icon.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport function FavIcon({\r\n  className,\r\n  url,\r\n  title,\r\n}: {\r\n  className?: string;\r\n  url: string;\r\n  title?: string;\r\n}) {\r\n  return (\r\n    <img\r\n      className={cn(\"bg-accent h-4 w-4 rounded-full shadow-sm\", className)}\r\n      width={16}\r\n      height={16}\r\n      src={new URL(url).origin + \"/favicon.ico\"}\r\n      alt={title}\r\n      onError={(e) => {\r\n        e.currentTarget.src =\r\n          \"https://perishablepress.com/wp/wp-content/images/2021/favicon-standard.png\";\r\n      }}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;;;AAEO,SAAS,QAAQ,EACtB,SAAS,EACT,GAAG,EACH,KAAK,EAKN;IACC,qBACE,sSAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QAC1D,OAAO;QACP,QAAQ;QACR,KAAK,IAAI,IAAI,KAAK,MAAM,GAAG;QAC3B,KAAK;QACL,SAAS,CAAC;YACR,EAAE,aAAa,CAAC,GAAG,GACjB;QACJ;;;;;;AAGN;KAtBgB", "debugId": null}}, {"offset": {"line": 3081, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\r\nimport { ChevronDownIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Accordion({\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\r\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />\r\n}\r\n\r\nfunction AccordionItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\r\n  return (\r\n    <AccordionPrimitive.Item\r\n      data-slot=\"accordion-item\"\r\n      className={cn(\"border-b last:border-b-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AccordionTrigger({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\r\n  return (\r\n    <AccordionPrimitive.Header className=\"flex\">\r\n      <AccordionPrimitive.Trigger\r\n        data-slot=\"accordion-trigger\"\r\n        className={cn(\r\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <ChevronDownIcon className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\r\n      </AccordionPrimitive.Trigger>\r\n    </AccordionPrimitive.Header>\r\n  )\r\n}\r\n\r\nfunction AccordionContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\r\n  return (\r\n    <AccordionPrimitive.Content\r\n      data-slot=\"accordion-content\"\r\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\r\n      {...props}\r\n    >\r\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\r\n    </AccordionPrimitive.Content>\r\n  )\r\n}\r\n\r\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,UAAU,EACjB,GAAG,OACkD;IACrD,qBAAO,sSAAC,+QAAA,CAAA,OAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;KAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,sSAAC,+QAAA,CAAA,OAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,sSAAC,+QAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,sSAAC,+QAAA,CAAA,UAA0B;YACzB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8SACA;YAED,GAAG,KAAK;;gBAER;8BACD,sSAAC,+SAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MApBS;AAsBT,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,sSAAC,+QAAA,CAAA,UAA0B;QACzB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,sSAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;AAGnD;MAdS", "debugId": null}}, {"offset": {"line": 3183, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"~/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 3214, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/chat/components/research-activities-block.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { PythonOutlined } from \"@ant-design/icons\";\r\nimport { motion } from \"framer-motion\";\r\nimport { LRUCache } from \"lru-cache\";\r\nimport { BookOpenText, PencilRuler, Search, Database, CheckCircle, Clock, Target } from \"lucide-react\";\r\nimport { useTheme } from \"next-themes\";\r\nimport { useMemo } from \"react\";\r\nimport SyntaxHighlighter from \"react-syntax-highlighter\";\r\nimport { docco } from \"react-syntax-highlighter/dist/esm/styles/hljs\";\r\nimport { dark } from \"react-syntax-highlighter/dist/esm/styles/prism\";\r\n\r\nimport { FavIcon } from \"~/components/deer-flow/fav-icon\";\r\nimport Image from \"~/components/deer-flow/image\";\r\nimport { LoadingAnimation } from \"~/components/deer-flow/loading-animation\";\r\nimport { Markdown } from \"~/components/deer-flow/markdown\";\r\nimport { RainbowText } from \"~/components/deer-flow/rainbow-text\";\r\nimport { Tooltip } from \"~/components/deer-flow/tooltip\";\r\nimport {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from \"~/components/ui/accordion\";\r\nimport { Skeleton } from \"~/components/ui/skeleton\";\r\nimport { findMCPTool } from \"~/core/mcp\";\r\nimport type { ToolCallRuntime } from \"~/core/messages\";\r\nimport { useMessage, useStore } from \"~/core/store\";\r\nimport { parseJSON } from \"~/core/utils\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport function ResearchActivitiesBlock({\r\n  className,\r\n  researchId,\r\n}: {\r\n  className?: string;\r\n  researchId: string;\r\n}) {\r\n  const activityIds = useStore((state) =>\r\n    state.researchActivityIds.get(researchId),\r\n  )!;\r\n  const ongoing = useStore((state) => state.ongoingResearchId === researchId);\r\n  return (\r\n    <>\r\n      <ul className={cn(\"flex flex-col py-4\", className)}>\r\n        {activityIds.map(\r\n          (activityId, i) =>\r\n            i !== 0 && (\r\n              <motion.li\r\n                key={activityId}\r\n                style={{ transition: \"all 0.4s ease-out\" }}\r\n                initial={{ opacity: 0, y: 24 }}\r\n                animate={{ opacity: 1, y: 0 }}\r\n                transition={{\r\n                  duration: 0.4,\r\n                  ease: \"easeOut\",\r\n                }}\r\n              >\r\n                <ActivityMessage messageId={activityId} />\r\n                <ActivityListItem messageId={activityId} />\r\n                {i !== activityIds.length - 1 && <hr className=\"my-8\" />}\r\n              </motion.li>\r\n            ),\r\n        )}\r\n      </ul>\r\n      {ongoing && <LoadingAnimation className=\"mx-4 my-12\" />}\r\n    </>\r\n  );\r\n}\r\n\r\nfunction ActivityMessage({ messageId }: { messageId: string }) {\r\n  const message = useMessage(messageId);\r\n  if (message?.agent && message.content) {\r\n    if (message.agent !== \"reporter\" && message.agent !== \"planner\") {\r\n      return (\r\n        <div className=\"px-4 py-2\">\r\n          <Markdown animated checkLinkCredibility>\r\n            {message.content}\r\n          </Markdown>\r\n        </div>\r\n      );\r\n    }\r\n  }\r\n  return null;\r\n}\r\n\r\nfunction ActivityListItem({ messageId }: { messageId: string }) {\r\n  const message = useMessage(messageId);\r\n  if (message) {\r\n    if (!message.isStreaming && message.toolCalls?.length) {\r\n      for (const toolCall of message.toolCalls) {\r\n        // Check if this is a KB progress tool call\r\n        if (toolCall.result) {\r\n          try {\r\n            const result = JSON.parse(toolCall.result);\r\n            if (result.type === \"kb_progress\") {\r\n              return <KBProgressToolCall key={toolCall.id} toolCall={toolCall} />;\r\n            }\r\n          } catch (e) {\r\n            // Not JSON or not KB progress, continue with existing logic\r\n          }\r\n        }\r\n\r\n        // Existing tool call handling\r\n        if (toolCall.name === \"web_search\") {\r\n          return <WebSearchToolCall key={toolCall.id} toolCall={toolCall} />;\r\n        } else if (toolCall.name === \"crawl_tool\") {\r\n          return <CrawlToolCall key={toolCall.id} toolCall={toolCall} />;\r\n        } else if (toolCall.name === \"python_repl_tool\") {\r\n          return <PythonToolCall key={toolCall.id} toolCall={toolCall} />;\r\n        } else if (toolCall.name === \"kb_database_store\") {\r\n          // KB specific tool - show storage confirmation\r\n          return <KBStorageToolCall key={toolCall.id} toolCall={toolCall} />;\r\n        } else if (toolCall.name === \"kb_url_evaluator\") {\r\n          // KB URL evaluation tool\r\n          return <KBEvaluationToolCall key={toolCall.id} toolCall={toolCall} />;\r\n        } else if (toolCall.name === \"kb_content_processor\") {\r\n          // KB content processing tool\r\n          return <KBProcessingToolCall key={toolCall.id} toolCall={toolCall} />;\r\n        } else {\r\n          return <MCPToolCall key={toolCall.id} toolCall={toolCall} />;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  return null;\r\n}\r\n\r\n// KB Progress Tool Call Component\r\nfunction KBProgressToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {\r\n  const progressData = useMemo(() => {\r\n    try {\r\n      const result = JSON.parse(toolCall.result || \"{}\");\r\n      if (result.type === \"kb_progress\" && result.data) {\r\n        return result.data;\r\n      }\r\n    } catch (e) {\r\n      // Not KB progress data\r\n    }\r\n    return null;\r\n  }, [toolCall.result]);\r\n\r\n  if (!progressData) {\r\n    return null; // Let other tool calls be handled normally\r\n  }\r\n\r\n  return (\r\n    <section className=\"mt-4 pl-4\">\r\n      <div className=\"flex items-center\">\r\n        <Database className=\"mr-2\" size={16} />\r\n        <RainbowText\r\n          className=\"text-base font-medium italic\"\r\n          animated={progressData.percentage < 100}\r\n        >\r\n          Knowledge Base Building Progress\r\n        </RainbowText>\r\n      </div>\r\n      \r\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4 mt-2 max-w-[calc(100%-120px)]\">\r\n        {/* Progress Bar */}\r\n        <div className=\"mb-4\">\r\n          <div className=\"flex justify-between text-sm text-gray-600 mb-1\">\r\n            <span>Articles Stored</span>\r\n            <span>{progressData.stored} / {progressData.target}</span>\r\n          </div>\r\n          <div className=\"w-full bg-gray-200 rounded-full h-2\">\r\n            <div \r\n              className=\"bg-blue-600 h-2 rounded-full transition-all duration-300\"\r\n              style={{ width: `${Math.min(100, progressData.percentage)}%` }}\r\n            />\r\n          </div>\r\n          <div className=\"text-xs text-gray-500 mt-1\">\r\n            {progressData.percentage.toFixed(1)}% complete\r\n          </div>\r\n        </div>\r\n\r\n        {/* Topic Breakdown */}\r\n        {Object.keys(progressData.by_topic || {}).length > 0 && (\r\n          <div className=\"grid grid-cols-2 gap-4 mb-3\">\r\n            <div className=\"space-y-1\">\r\n              <h4 className=\"text-sm font-medium text-gray-700\">By Topic:</h4>\r\n              {Object.entries(progressData.by_topic).map(([topic, count]) => (\r\n                <div key={topic} className=\"flex justify-between text-sm\">\r\n                  <span className=\"text-gray-600 capitalize\">{topic}</span>\r\n                  <span className=\"font-medium\">{String(count)}</span>\r\n                </div>\r\n              ))}\r\n            </div>\r\n            \r\n            <div className=\"space-y-1\">\r\n              <h4 className=\"text-sm font-medium text-gray-700\">Queue Status:</h4>\r\n              <div className=\"flex items-center gap-2 text-sm\">\r\n                <Clock size={14} className=\"text-yellow-500\" />\r\n                <span>Pending: {progressData.pending || 0}</span>\r\n              </div>\r\n              <div className=\"flex items-center gap-2 text-sm\">\r\n                <Target size={14} className=\"text-gray-500\" />\r\n                <span>Processing: {progressData.processing || 0}</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n        \r\n        {/* Status */}\r\n        <div className=\"flex items-center gap-2 text-sm\">\r\n          {progressData.percentage >= 100 ? (\r\n            <>\r\n              <CheckCircle size={16} className=\"text-green-500\" />\r\n              <span className=\"text-green-700 font-medium\">Knowledge base complete!</span>\r\n            </>\r\n          ) : (\r\n            <>\r\n              <Clock size={16} className=\"text-blue-500\" />\r\n              <span className=\"text-blue-700\">Building knowledge base...</span>\r\n            </>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\n// KB Storage Tool Call Component\r\nfunction KBStorageToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {\r\n  return (\r\n    <section className=\"mt-4 pl-4\">\r\n      <div className=\"flex items-center\">\r\n        <Database className=\"mr-2\" size={16} />\r\n        <RainbowText\r\n          className=\"text-base font-medium italic\"\r\n          animated={toolCall.result === undefined}\r\n        >\r\n          Storing Knowledge Article\r\n        </RainbowText>\r\n      </div>\r\n      {toolCall.result && (\r\n        <div className=\"bg-green-50 border border-green-200 rounded-lg p-2 mt-2 text-sm text-green-700 max-w-[calc(100%-120px)]\">\r\n          ✅ {typeof toolCall.result === 'string' ? toolCall.result : 'Article stored successfully'}\r\n        </div>\r\n      )}\r\n    </section>\r\n  );\r\n}\r\n\r\n// KB Evaluation Tool Call Component\r\nfunction KBEvaluationToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {\r\n  return (\r\n    <section className=\"mt-4 pl-4\">\r\n      <div className=\"flex items-center\">\r\n        <Search className=\"mr-2\" size={16} />\r\n        <RainbowText\r\n          className=\"text-base font-medium italic\"\r\n          animated={toolCall.result === undefined}\r\n        >\r\n          Evaluating Knowledge Sources\r\n        </RainbowText>\r\n      </div>\r\n      {toolCall.result && (\r\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-2 mt-2 text-sm text-yellow-700 max-w-[calc(100%-120px)]\">\r\n          📋 {toolCall.result}\r\n        </div>\r\n      )}\r\n    </section>\r\n  );\r\n}\r\n\r\n// KB Processing Tool Call Component\r\nfunction KBProcessingToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {\r\n  return (\r\n    <section className=\"mt-4 pl-4\">\r\n      <div className=\"flex items-center\">\r\n        <PencilRuler className=\"mr-2\" size={16} />\r\n        <RainbowText\r\n          className=\"text-base font-medium italic\"\r\n          animated={toolCall.result === undefined}\r\n        >\r\n          Processing Knowledge Content\r\n        </RainbowText>\r\n      </div>\r\n      {toolCall.result && (\r\n        <div className=\"bg-orange-50 border border-orange-200 rounded-lg p-2 mt-2 text-sm text-orange-700 max-w-[calc(100%-120px)]\">\r\n          ⚙️ {toolCall.result}\r\n        </div>\r\n      )}\r\n    </section>\r\n  );\r\n}\r\n\r\nconst __pageCache = new LRUCache<string, string>({ max: 100 });\r\ntype SearchResult =\r\n  | {\r\n      type: \"page\";\r\n      title: string;\r\n      url: string;\r\n      content: string;\r\n    }\r\n  | {\r\n      type: \"image\";\r\n      image_url: string;\r\n      image_description: string;\r\n    };\r\nfunction WebSearchToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {\r\n  const searching = useMemo(() => {\r\n    return toolCall.result === undefined;\r\n  }, [toolCall.result]);\r\n  const searchResults = useMemo<SearchResult[]>(() => {\r\n    let results: SearchResult[] | undefined = undefined;\r\n    try {\r\n      results = toolCall.result ? parseJSON(toolCall.result, []) : undefined;\r\n    } catch {\r\n      results = undefined;\r\n    }\r\n    if (Array.isArray(results)) {\r\n      results.forEach((result) => {\r\n        if (result.type === \"page\") {\r\n          __pageCache.set(result.url, result.title);\r\n        }\r\n      });\r\n    } else {\r\n      results = [];\r\n    }\r\n    return results;\r\n  }, [toolCall.result]);\r\n  const pageResults = useMemo(\r\n    () => searchResults?.filter((result) => result.type === \"page\"),\r\n    [searchResults],\r\n  );\r\n  const imageResults = useMemo(\r\n    () => searchResults?.filter((result) => result.type === \"image\"),\r\n    [searchResults],\r\n  );\r\n  return (\r\n    <section className=\"mt-4 pl-4\">\r\n      <div className=\"font-medium italic\">\r\n        <RainbowText\r\n          className=\"flex items-center\"\r\n          animated={searchResults === undefined}\r\n        >\r\n          <Search size={16} className={\"mr-2\"} />\r\n          <span>Searching for&nbsp;</span>\r\n          <span className=\"max-w-[500px] overflow-hidden text-ellipsis whitespace-nowrap\">\r\n            {(toolCall.args as { query: string }).query}\r\n          </span>\r\n        </RainbowText>\r\n      </div>\r\n      <div className=\"pr-4\">\r\n        {pageResults && (\r\n          <ul className=\"mt-2 flex flex-wrap gap-4\">\r\n            {searching &&\r\n              [...Array(6)].map((_, i) => (\r\n                <li\r\n                  key={`search-result-${i}`}\r\n                  className=\"flex h-40 w-40 gap-2 rounded-md text-sm\"\r\n                >\r\n                  <Skeleton\r\n                    className=\"to-accent h-full w-full rounded-md bg-gradient-to-tl from-slate-400\"\r\n                    style={{ animationDelay: `${i * 0.2}s` }}\r\n                  />\r\n                </li>\r\n              ))}\r\n            {pageResults\r\n              .filter((result) => result.type === \"page\")\r\n              .map((searchResult, i) => (\r\n                <motion.li\r\n                  key={`search-result-${i}`}\r\n                  className=\"text-muted-foreground bg-accent flex max-w-40 gap-2 rounded-md px-2 py-1 text-sm\"\r\n                  initial={{ opacity: 0, y: 10, scale: 0.66 }}\r\n                  animate={{ opacity: 1, y: 0, scale: 1 }}\r\n                  transition={{\r\n                    duration: 0.2,\r\n                    delay: i * 0.1,\r\n                    ease: \"easeOut\",\r\n                  }}\r\n                >\r\n                  <FavIcon\r\n                    className=\"mt-1\"\r\n                    url={searchResult.url}\r\n                    title={searchResult.title}\r\n                  />\r\n                  <a href={searchResult.url} target=\"_blank\">\r\n                    {searchResult.title}\r\n                  </a>\r\n                </motion.li>\r\n              ))}\r\n            {imageResults.map((searchResult, i) => (\r\n              <motion.li\r\n                key={`search-result-${i}`}\r\n                initial={{ opacity: 0, y: 10, scale: 0.66 }}\r\n                animate={{ opacity: 1, y: 0, scale: 1 }}\r\n                transition={{\r\n                  duration: 0.2,\r\n                  delay: i * 0.1,\r\n                  ease: \"easeOut\",\r\n                }}\r\n              >\r\n                <a\r\n                  className=\"flex flex-col gap-2 overflow-hidden rounded-md opacity-75 transition-opacity duration-300 hover:opacity-100\"\r\n                  href={searchResult.image_url}\r\n                  target=\"_blank\"\r\n                >\r\n                  <Image\r\n                    src={searchResult.image_url}\r\n                    alt={searchResult.image_description}\r\n                    className=\"bg-accent h-40 w-40 max-w-full rounded-md bg-cover bg-center bg-no-repeat\"\r\n                    imageClassName=\"hover:scale-110\"\r\n                    imageTransition\r\n                  />\r\n                </a>\r\n              </motion.li>\r\n            ))}\r\n          </ul>\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nfunction CrawlToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {\r\n  const url = useMemo(\r\n    () => (toolCall.args as { url: string }).url,\r\n    [toolCall.args],\r\n  );\r\n  const title = useMemo(() => __pageCache.get(url), [url]);\r\n  return (\r\n    <section className=\"mt-4 pl-4\">\r\n      <div>\r\n        <RainbowText\r\n          className=\"flex items-center text-base font-medium italic\"\r\n          animated={toolCall.result === undefined}\r\n        >\r\n          <BookOpenText size={16} className={\"mr-2\"} />\r\n          <span>Reading</span>\r\n        </RainbowText>\r\n      </div>\r\n      <ul className=\"mt-2 flex flex-wrap gap-4\">\r\n        <motion.li\r\n          className=\"text-muted-foreground bg-accent flex h-40 w-40 gap-2 rounded-md px-2 py-1 text-sm\"\r\n          initial={{ opacity: 0, y: 10, scale: 0.66 }}\r\n          animate={{ opacity: 1, y: 0, scale: 1 }}\r\n          transition={{\r\n            duration: 0.2,\r\n            ease: \"easeOut\",\r\n          }}\r\n        >\r\n          <FavIcon className=\"mt-1\" url={url} title={title} />\r\n          <a\r\n            className=\"h-full flex-grow overflow-hidden text-ellipsis whitespace-nowrap\"\r\n            href={url}\r\n            target=\"_blank\"\r\n          >\r\n            {title ?? url}\r\n          </a>\r\n        </motion.li>\r\n      </ul>\r\n    </section>\r\n  );\r\n}\r\n\r\nfunction PythonToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {\r\n  const code = useMemo<string>(() => {\r\n    return (toolCall.args as { code: string }).code;\r\n  }, [toolCall.args]);\r\n  const { resolvedTheme } = useTheme();\r\n  return (\r\n    <section className=\"mt-4 pl-4\">\r\n      <div className=\"flex items-center\">\r\n        <PythonOutlined className={\"mr-2\"} />\r\n        <RainbowText\r\n          className=\"text-base font-medium italic\"\r\n          animated={toolCall.result === undefined}\r\n        >\r\n          Running Python code\r\n        </RainbowText>\r\n      </div>\r\n      <div>\r\n        <div className=\"bg-accent mt-2 max-h-[400px] max-w-[calc(100%-120px)] overflow-y-auto rounded-md p-2 text-sm\">\r\n          <SyntaxHighlighter\r\n            language=\"python\"\r\n            style={resolvedTheme === \"dark\" ? dark : docco}\r\n            customStyle={{\r\n              background: \"transparent\",\r\n              border: \"none\",\r\n              boxShadow: \"none\",\r\n            }}\r\n          >\r\n            {code.trim()}\r\n          </SyntaxHighlighter>\r\n        </div>\r\n      </div>\r\n      {toolCall.result && <PythonToolCallResult result={toolCall.result} />}\r\n    </section>\r\n  );\r\n}\r\n\r\nfunction PythonToolCallResult({ result }: { result: string }) {\r\n  const { resolvedTheme } = useTheme();\r\n  const hasError = useMemo(\r\n    () => result.includes(\"Error executing code:\\n\"),\r\n    [result],\r\n  );\r\n  const error = useMemo(() => {\r\n    if (hasError) {\r\n      const parts = result.split(\"```\\nError: \");\r\n      if (parts.length > 1) {\r\n        return parts[1]!.trim();\r\n      }\r\n    }\r\n    return null;\r\n  }, [result, hasError]);\r\n  const stdout = useMemo(() => {\r\n    if (!hasError) {\r\n      const parts = result.split(\"```\\nStdout: \");\r\n      if (parts.length > 1) {\r\n        return parts[1]!.trim();\r\n      }\r\n    }\r\n    return null;\r\n  }, [result, hasError]);\r\n  return (\r\n    <>\r\n      <div className=\"mt-4 font-medium italic\">\r\n        {hasError ? \"Error when executing the above code\" : \"Execution output\"}\r\n      </div>\r\n      <div className=\"bg-accent mt-2 max-h-[400px] max-w-[calc(100%-120px)] overflow-y-auto rounded-md p-2 text-sm\">\r\n        <SyntaxHighlighter\r\n          language=\"plaintext\"\r\n          style={resolvedTheme === \"dark\" ? dark : docco}\r\n          customStyle={{\r\n            color: hasError ? \"red\" : \"inherit\",\r\n            background: \"transparent\",\r\n            border: \"none\",\r\n            boxShadow: \"none\",\r\n          }}\r\n        >\r\n          {error ?? stdout ?? \"(empty)\"}\r\n        </SyntaxHighlighter>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n\r\nfunction MCPToolCall({ toolCall }: { toolCall: ToolCallRuntime }) {\r\n  const tool = useMemo(() => findMCPTool(toolCall.name), [toolCall.name]);\r\n  const { resolvedTheme } = useTheme();\r\n  return (\r\n    <section className=\"mt-4 pl-4\">\r\n      <div className=\"w-fit overflow-y-auto rounded-md py-0\">\r\n        <Accordion type=\"single\" collapsible className=\"w-full\">\r\n          <AccordionItem value=\"item-1\">\r\n            <AccordionTrigger>\r\n              <Tooltip title={tool?.description}>\r\n                <div className=\"flex items-center font-medium italic\">\r\n                  <PencilRuler size={16} className={\"mr-2\"} />\r\n                  <RainbowText\r\n                    className=\"pr-0.5 text-base font-medium italic\"\r\n                    animated={toolCall.result === undefined}\r\n                  >\r\n                    Running {toolCall.name ? toolCall.name + \"()\" : \"MCP tool\"}\r\n                  </RainbowText>\r\n                </div>\r\n              </Tooltip>\r\n            </AccordionTrigger>\r\n            <AccordionContent>\r\n              {toolCall.result && (\r\n                <div className=\"bg-accent max-h-[400px] max-w-[560px] overflow-y-auto rounded-md text-sm\">\r\n                  <SyntaxHighlighter\r\n                    language=\"json\"\r\n                    style={resolvedTheme === \"dark\" ? dark : docco}\r\n                    customStyle={{\r\n                      background: \"transparent\",\r\n                      border: \"none\",\r\n                      boxShadow: \"none\",\r\n                    }}\r\n                  >\r\n                    {toolCall.result.trim()}\r\n                  </SyntaxHighlighter>\r\n                </div>\r\n              )}\r\n            </AccordionContent>\r\n          </AccordionItem>\r\n        </Accordion>\r\n      </div>\r\n    </section>\r\n  );\r\n}"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAAA;AAEA;AAAA;AACA;AAAA;AACA;;;;;;;;;;;;;;;;;;;;;;;;AAEO,SAAS,wBAAwB,EACtC,SAAS,EACT,UAAU,EAIX;;IACC,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;yDAAE,CAAC,QAC5B,MAAM,mBAAmB,CAAC,GAAG,CAAC;;IAEhC,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;qDAAE,CAAC,QAAU,MAAM,iBAAiB,KAAK;;IAChE,qBACE;;0BACE,sSAAC;gBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB;0BACrC,YAAY,GAAG,CACd,CAAC,YAAY,IACX,MAAM,mBACJ,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;wBAER,OAAO;4BAAE,YAAY;wBAAoB;wBACzC,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BACV,UAAU;4BACV,MAAM;wBACR;;0CAEA,sSAAC;gCAAgB,WAAW;;;;;;0CAC5B,sSAAC;gCAAiB,WAAW;;;;;;4BAC5B,MAAM,YAAY,MAAM,GAAG,mBAAK,sSAAC;gCAAG,WAAU;;;;;;;uBAX1C;;;;;;;;;;YAgBd,yBAAW,sSAAC,6JAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;AAG9C;GArCgB;;QAOM,gIAAA,CAAA,WAAQ;QAGZ,gIAAA,CAAA,WAAQ;;;KAVV;AAuChB,SAAS,gBAAgB,EAAE,SAAS,EAAyB;;IAC3D,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,SAAS,SAAS,QAAQ,OAAO,EAAE;QACrC,IAAI,QAAQ,KAAK,KAAK,cAAc,QAAQ,KAAK,KAAK,WAAW;YAC/D,qBACE,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,iJAAA,CAAA,WAAQ;oBAAC,QAAQ;oBAAC,oBAAoB;8BACpC,QAAQ,OAAO;;;;;;;;;;;QAIxB;IACF;IACA,OAAO;AACT;IAdS;;QACS,gIAAA,CAAA,aAAU;;;MADnB;AAgBT,SAAS,iBAAiB,EAAE,SAAS,EAAyB;;IAC5D,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,SAAS;QACX,IAAI,CAAC,QAAQ,WAAW,IAAI,QAAQ,SAAS,EAAE,QAAQ;YACrD,KAAK,MAAM,YAAY,QAAQ,SAAS,CAAE;gBACxC,2CAA2C;gBAC3C,IAAI,SAAS,MAAM,EAAE;oBACnB,IAAI;wBACF,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,MAAM;wBACzC,IAAI,OAAO,IAAI,KAAK,eAAe;4BACjC,qBAAO,sSAAC;gCAAqC,UAAU;+BAAvB,SAAS,EAAE;;;;;wBAC7C;oBACF,EAAE,OAAO,GAAG;oBACV,4DAA4D;oBAC9D;gBACF;gBAEA,8BAA8B;gBAC9B,IAAI,SAAS,IAAI,KAAK,cAAc;oBAClC,qBAAO,sSAAC;wBAAoC,UAAU;uBAAvB,SAAS,EAAE;;;;;gBAC5C,OAAO,IAAI,SAAS,IAAI,KAAK,cAAc;oBACzC,qBAAO,sSAAC;wBAAgC,UAAU;uBAAvB,SAAS,EAAE;;;;;gBACxC,OAAO,IAAI,SAAS,IAAI,KAAK,oBAAoB;oBAC/C,qBAAO,sSAAC;wBAAiC,UAAU;uBAAvB,SAAS,EAAE;;;;;gBACzC,OAAO,IAAI,SAAS,IAAI,KAAK,qBAAqB;oBAChD,+CAA+C;oBAC/C,qBAAO,sSAAC;wBAAoC,UAAU;uBAAvB,SAAS,EAAE;;;;;gBAC5C,OAAO,IAAI,SAAS,IAAI,KAAK,oBAAoB;oBAC/C,yBAAyB;oBACzB,qBAAO,sSAAC;wBAAuC,UAAU;uBAAvB,SAAS,EAAE;;;;;gBAC/C,OAAO,IAAI,SAAS,IAAI,KAAK,wBAAwB;oBACnD,6BAA6B;oBAC7B,qBAAO,sSAAC;wBAAuC,UAAU;uBAAvB,SAAS,EAAE;;;;;gBAC/C,OAAO;oBACL,qBAAO,sSAAC;wBAA8B,UAAU;uBAAvB,SAAS,EAAE;;;;;gBACtC;YACF;QACF;IACF;IACA,OAAO;AACT;IAxCS;;QACS,gIAAA,CAAA,aAAU;;;MADnB;AA0CT,kCAAkC;AAClC,SAAS,mBAAmB,EAAE,QAAQ,EAAiC;;IACrE,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;oDAAE;YAC3B,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC,SAAS,MAAM,IAAI;gBAC7C,IAAI,OAAO,IAAI,KAAK,iBAAiB,OAAO,IAAI,EAAE;oBAChD,OAAO,OAAO,IAAI;gBACpB;YACF,EAAE,OAAO,GAAG;YACV,uBAAuB;YACzB;YACA,OAAO;QACT;mDAAG;QAAC,SAAS,MAAM;KAAC;IAEpB,IAAI,CAAC,cAAc;QACjB,OAAO,MAAM,2CAA2C;IAC1D;IAEA,qBACE,sSAAC;QAAQ,WAAU;;0BACjB,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,iSAAA,CAAA,WAAQ;wBAAC,WAAU;wBAAO,MAAM;;;;;;kCACjC,sSAAC,wJAAA,CAAA,cAAW;wBACV,WAAU;wBACV,UAAU,aAAa,UAAU,GAAG;kCACrC;;;;;;;;;;;;0BAKH,sSAAC;gBAAI,WAAU;;kCAEb,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;kDAAK;;;;;;kDACN,sSAAC;;4CAAM,aAAa,MAAM;4CAAC;4CAAI,aAAa,MAAM;;;;;;;;;;;;;0CAEpD,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,KAAK,GAAG,CAAC,KAAK,aAAa,UAAU,EAAE,CAAC,CAAC;oCAAC;;;;;;;;;;;0CAGjE,sSAAC;gCAAI,WAAU;;oCACZ,aAAa,UAAU,CAAC,OAAO,CAAC;oCAAG;;;;;;;;;;;;;oBAKvC,OAAO,IAAI,CAAC,aAAa,QAAQ,IAAI,CAAC,GAAG,MAAM,GAAG,mBACjD,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAG,WAAU;kDAAoC;;;;;;oCACjD,OAAO,OAAO,CAAC,aAAa,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,OAAO,MAAM,iBACxD,sSAAC;4CAAgB,WAAU;;8DACzB,sSAAC;oDAAK,WAAU;8DAA4B;;;;;;8DAC5C,sSAAC;oDAAK,WAAU;8DAAe,OAAO;;;;;;;2CAF9B;;;;;;;;;;;0CAOd,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,2RAAA,CAAA,QAAK;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC3B,sSAAC;;oDAAK;oDAAU,aAAa,OAAO,IAAI;;;;;;;;;;;;;kDAE1C,sSAAC;wCAAI,WAAU;;0DACb,sSAAC,6RAAA,CAAA,SAAM;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC5B,sSAAC;;oDAAK;oDAAa,aAAa,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;kCAOtD,sSAAC;wBAAI,WAAU;kCACZ,aAAa,UAAU,IAAI,oBAC1B;;8CACE,sSAAC,kTAAA,CAAA,cAAW;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CACjC,sSAAC;oCAAK,WAAU;8CAA6B;;;;;;;yDAG/C;;8CACE,sSAAC,2RAAA,CAAA,QAAK;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC3B,sSAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;AAO9C;IA3FS;MAAA;AA6FT,iCAAiC;AACjC,SAAS,kBAAkB,EAAE,QAAQ,EAAiC;IACpE,qBACE,sSAAC;QAAQ,WAAU;;0BACjB,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,iSAAA,CAAA,WAAQ;wBAAC,WAAU;wBAAO,MAAM;;;;;;kCACjC,sSAAC,wJAAA,CAAA,cAAW;wBACV,WAAU;wBACV,UAAU,SAAS,MAAM,KAAK;kCAC/B;;;;;;;;;;;;YAIF,SAAS,MAAM,kBACd,sSAAC;gBAAI,WAAU;;oBAA0G;oBACpH,OAAO,SAAS,MAAM,KAAK,WAAW,SAAS,MAAM,GAAG;;;;;;;;;;;;;AAKrE;MAnBS;AAqBT,oCAAoC;AACpC,SAAS,qBAAqB,EAAE,QAAQ,EAAiC;IACvE,qBACE,sSAAC;QAAQ,WAAU;;0BACjB,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,6RAAA,CAAA,SAAM;wBAAC,WAAU;wBAAO,MAAM;;;;;;kCAC/B,sSAAC,wJAAA,CAAA,cAAW;wBACV,WAAU;wBACV,UAAU,SAAS,MAAM,KAAK;kCAC/B;;;;;;;;;;;;YAIF,SAAS,MAAM,kBACd,sSAAC;gBAAI,WAAU;;oBAA6G;oBACtH,SAAS,MAAM;;;;;;;;;;;;;AAK7B;MAnBS;AAqBT,oCAAoC;AACpC,SAAS,qBAAqB,EAAE,QAAQ,EAAiC;IACvE,qBACE,sSAAC;QAAQ,WAAU;;0BACjB,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,2SAAA,CAAA,cAAW;wBAAC,WAAU;wBAAO,MAAM;;;;;;kCACpC,sSAAC,wJAAA,CAAA,cAAW;wBACV,WAAU;wBACV,UAAU,SAAS,MAAM,KAAK;kCAC/B;;;;;;;;;;;;YAIF,SAAS,MAAM,kBACd,sSAAC;gBAAI,WAAU;;oBAA6G;oBACtH,SAAS,MAAM;;;;;;;;;;;;;AAK7B;MAnBS;AAqBT,MAAM,cAAc,IAAI,iNAAA,CAAA,WAAQ,CAAiB;IAAE,KAAK;AAAI;AAa5D,SAAS,kBAAkB,EAAE,QAAQ,EAAiC;;IACpE,MAAM,YAAY,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;gDAAE;YACxB,OAAO,SAAS,MAAM,KAAK;QAC7B;+CAAG;QAAC,SAAS,MAAM;KAAC;IACpB,MAAM,gBAAgB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;oDAAkB;YAC5C,IAAI,UAAsC;YAC1C,IAAI;gBACF,UAAU,SAAS,MAAM,GAAG,CAAA,GAAA,+HAAA,CAAA,YAAS,AAAD,EAAE,SAAS,MAAM,EAAE,EAAE,IAAI;YAC/D,EAAE,OAAM;gBACN,UAAU;YACZ;YACA,IAAI,MAAM,OAAO,CAAC,UAAU;gBAC1B,QAAQ,OAAO;gEAAC,CAAC;wBACf,IAAI,OAAO,IAAI,KAAK,QAAQ;4BAC1B,YAAY,GAAG,CAAC,OAAO,GAAG,EAAE,OAAO,KAAK;wBAC1C;oBACF;;YACF,OAAO;gBACL,UAAU,EAAE;YACd;YACA,OAAO;QACT;mDAAG;QAAC,SAAS,MAAM;KAAC;IACpB,MAAM,cAAc,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;kDACxB,IAAM,eAAe;0DAAO,CAAC,SAAW,OAAO,IAAI,KAAK;;iDACxD;QAAC;KAAc;IAEjB,MAAM,eAAe,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;mDACzB,IAAM,eAAe;2DAAO,CAAC,SAAW,OAAO,IAAI,KAAK;;kDACxD;QAAC;KAAc;IAEjB,qBACE,sSAAC;QAAQ,WAAU;;0BACjB,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,wJAAA,CAAA,cAAW;oBACV,WAAU;oBACV,UAAU,kBAAkB;;sCAE5B,sSAAC,6RAAA,CAAA,SAAM;4BAAC,MAAM;4BAAI,WAAW;;;;;;sCAC7B,sSAAC;sCAAK;;;;;;sCACN,sSAAC;4BAAK,WAAU;sCACb,AAAC,SAAS,IAAI,CAAuB,KAAK;;;;;;;;;;;;;;;;;0BAIjD,sSAAC;gBAAI,WAAU;0BACZ,6BACC,sSAAC;oBAAG,WAAU;;wBACX,aACC;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACpB,sSAAC;gCAEC,WAAU;0CAEV,cAAA,sSAAC,uIAAA,CAAA,WAAQ;oCACP,WAAU;oCACV,OAAO;wCAAE,gBAAgB,GAAG,IAAI,IAAI,CAAC,CAAC;oCAAC;;;;;;+BALpC,CAAC,cAAc,EAAE,GAAG;;;;;wBAS9B,YACE,MAAM,CAAC,CAAC,SAAW,OAAO,IAAI,KAAK,QACnC,GAAG,CAAC,CAAC,cAAc,kBAClB,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;gCAER,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;oCAAI,OAAO;gCAAK;gCAC1C,SAAS;oCAAE,SAAS;oCAAG,GAAG;oCAAG,OAAO;gCAAE;gCACtC,YAAY;oCACV,UAAU;oCACV,OAAO,IAAI;oCACX,MAAM;gCACR;;kDAEA,sSAAC,oJAAA,CAAA,UAAO;wCACN,WAAU;wCACV,KAAK,aAAa,GAAG;wCACrB,OAAO,aAAa,KAAK;;;;;;kDAE3B,sSAAC;wCAAE,MAAM,aAAa,GAAG;wCAAE,QAAO;kDAC/B,aAAa,KAAK;;;;;;;+BAhBhB,CAAC,cAAc,EAAE,GAAG;;;;;wBAoB9B,aAAa,GAAG,CAAC,CAAC,cAAc,kBAC/B,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;gCAER,SAAS;oCAAE,SAAS;oCAAG,GAAG;oCAAI,OAAO;gCAAK;gCAC1C,SAAS;oCAAE,SAAS;oCAAG,GAAG;oCAAG,OAAO;gCAAE;gCACtC,YAAY;oCACV,UAAU;oCACV,OAAO,IAAI;oCACX,MAAM;gCACR;0CAEA,cAAA,sSAAC;oCACC,WAAU;oCACV,MAAM,aAAa,SAAS;oCAC5B,QAAO;8CAEP,cAAA,sSAAC,8IAAA,CAAA,UAAK;wCACJ,KAAK,aAAa,SAAS;wCAC3B,KAAK,aAAa,iBAAiB;wCACnC,WAAU;wCACV,gBAAe;wCACf,eAAe;;;;;;;;;;;+BAnBd,CAAC,cAAc,EAAE,GAAG;;;;;;;;;;;;;;;;;;;;;;AA6BzC;IAlHS;MAAA;AAoHT,SAAS,cAAc,EAAE,QAAQ,EAAiC;;IAChE,MAAM,MAAM,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;sCAChB,IAAM,AAAC,SAAS,IAAI,CAAqB,GAAG;qCAC5C;QAAC,SAAS,IAAI;KAAC;IAEjB,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;wCAAE,IAAM,YAAY,GAAG,CAAC;uCAAM;QAAC;KAAI;IACvD,qBACE,sSAAC;QAAQ,WAAU;;0BACjB,sSAAC;0BACC,cAAA,sSAAC,wJAAA,CAAA,cAAW;oBACV,WAAU;oBACV,UAAU,SAAS,MAAM,KAAK;;sCAE9B,sSAAC,iTAAA,CAAA,eAAY;4BAAC,MAAM;4BAAI,WAAW;;;;;;sCACnC,sSAAC;sCAAK;;;;;;;;;;;;;;;;;0BAGV,sSAAC;gBAAG,WAAU;0BACZ,cAAA,sSAAC,sSAAA,CAAA,SAAM,CAAC,EAAE;oBACR,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;wBAAI,OAAO;oBAAK;oBAC1C,SAAS;wBAAE,SAAS;wBAAG,GAAG;wBAAG,OAAO;oBAAE;oBACtC,YAAY;wBACV,UAAU;wBACV,MAAM;oBACR;;sCAEA,sSAAC,oJAAA,CAAA,UAAO;4BAAC,WAAU;4BAAO,KAAK;4BAAK,OAAO;;;;;;sCAC3C,sSAAC;4BACC,WAAU;4BACV,MAAM;4BACN,QAAO;sCAEN,SAAS;;;;;;;;;;;;;;;;;;;;;;;AAMtB;IAvCS;MAAA;AAyCT,SAAS,eAAe,EAAE,QAAQ,EAAiC;;IACjE,MAAM,OAAO,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;wCAAU;YAC3B,OAAO,AAAC,SAAS,IAAI,CAAsB,IAAI;QACjD;uCAAG;QAAC,SAAS,IAAI;KAAC;IAClB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IACjC,qBACE,sSAAC;QAAQ,WAAU;;0BACjB,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,mUAAA,CAAA,iBAAc;wBAAC,WAAW;;;;;;kCAC3B,sSAAC,wJAAA,CAAA,cAAW;wBACV,WAAU;wBACV,UAAU,SAAS,MAAM,KAAK;kCAC/B;;;;;;;;;;;;0BAIH,sSAAC;0BACC,cAAA,sSAAC;oBAAI,WAAU;8BACb,cAAA,sSAAC,0RAAA,CAAA,UAAiB;wBAChB,UAAS;wBACT,OAAO,kBAAkB,SAAS,gUAAA,CAAA,OAAI,GAAG,iUAAA,CAAA,QAAK;wBAC9C,aAAa;4BACX,YAAY;4BACZ,QAAQ;4BACR,WAAW;wBACb;kCAEC,KAAK,IAAI;;;;;;;;;;;;;;;;YAIf,SAAS,MAAM,kBAAI,sSAAC;gBAAqB,QAAQ,SAAS,MAAM;;;;;;;;;;;;AAGvE;IAlCS;;QAImB,4PAAA,CAAA,WAAQ;;;MAJ3B;AAoCT,SAAS,qBAAqB,EAAE,MAAM,EAAsB;;IAC1D,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;kDACrB,IAAM,OAAO,QAAQ,CAAC;iDACtB;QAAC;KAAO;IAEV,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;+CAAE;YACpB,IAAI,UAAU;gBACZ,MAAM,QAAQ,OAAO,KAAK,CAAC;gBAC3B,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,OAAO,KAAK,CAAC,EAAE,CAAE,IAAI;gBACvB;YACF;YACA,OAAO;QACT;8CAAG;QAAC;QAAQ;KAAS;IACrB,MAAM,SAAS,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;gDAAE;YACrB,IAAI,CAAC,UAAU;gBACb,MAAM,QAAQ,OAAO,KAAK,CAAC;gBAC3B,IAAI,MAAM,MAAM,GAAG,GAAG;oBACpB,OAAO,KAAK,CAAC,EAAE,CAAE,IAAI;gBACvB;YACF;YACA,OAAO;QACT;+CAAG;QAAC;QAAQ;KAAS;IACrB,qBACE;;0BACE,sSAAC;gBAAI,WAAU;0BACZ,WAAW,wCAAwC;;;;;;0BAEtD,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,0RAAA,CAAA,UAAiB;oBAChB,UAAS;oBACT,OAAO,kBAAkB,SAAS,gUAAA,CAAA,OAAI,GAAG,iUAAA,CAAA,QAAK;oBAC9C,aAAa;wBACX,OAAO,WAAW,QAAQ;wBAC1B,YAAY;wBACZ,QAAQ;wBACR,WAAW;oBACb;8BAEC,SAAS,UAAU;;;;;;;;;;;;;AAK9B;IA7CS;;QACmB,4PAAA,CAAA,WAAQ;;;OAD3B;AA+CT,SAAS,YAAY,EAAE,QAAQ,EAAiC;;IAC9D,MAAM,OAAO,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;qCAAE,IAAM,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,SAAS,IAAI;oCAAG;QAAC,SAAS,IAAI;KAAC;IACtE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4PAAA,CAAA,WAAQ,AAAD;IACjC,qBACE,sSAAC;QAAQ,WAAU;kBACjB,cAAA,sSAAC;YAAI,WAAU;sBACb,cAAA,sSAAC,wIAAA,CAAA,YAAS;gBAAC,MAAK;gBAAS,WAAW;gBAAC,WAAU;0BAC7C,cAAA,sSAAC,wIAAA,CAAA,gBAAa;oBAAC,OAAM;;sCACnB,sSAAC,wIAAA,CAAA,mBAAgB;sCACf,cAAA,sSAAC,gJAAA,CAAA,UAAO;gCAAC,OAAO,MAAM;0CACpB,cAAA,sSAAC;oCAAI,WAAU;;sDACb,sSAAC,2SAAA,CAAA,cAAW;4CAAC,MAAM;4CAAI,WAAW;;;;;;sDAClC,sSAAC,wJAAA,CAAA,cAAW;4CACV,WAAU;4CACV,UAAU,SAAS,MAAM,KAAK;;gDAC/B;gDACU,SAAS,IAAI,GAAG,SAAS,IAAI,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;sCAKxD,sSAAC,wIAAA,CAAA,mBAAgB;sCACd,SAAS,MAAM,kBACd,sSAAC;gCAAI,WAAU;0CACb,cAAA,sSAAC,0RAAA,CAAA,UAAiB;oCAChB,UAAS;oCACT,OAAO,kBAAkB,SAAS,gUAAA,CAAA,OAAI,GAAG,iUAAA,CAAA,QAAK;oCAC9C,aAAa;wCACX,YAAY;wCACZ,QAAQ;wCACR,WAAW;oCACb;8CAEC,SAAS,MAAM,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzC;IA3CS;;QAEmB,4PAAA,CAAA,WAAQ;;;OAF3B", "debugId": null}}, {"offset": {"line": 4551, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/editor/extensions.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport {\r\n  AIHighlight,\r\n  CharacterCount,\r\n  CodeBlockLowlight,\r\n  Color,\r\n  CustomKeymap,\r\n  GlobalDragHandle,\r\n  HighlightExtension,\r\n  HorizontalRule,\r\n  Mathematics,\r\n  Placeholder,\r\n  StarterKit,\r\n  TaskItem,\r\n  TaskList,\r\n  TextStyle,\r\n  TiptapImage,\r\n  TiptapLink,\r\n  TiptapUnderline,\r\n  Twitter,\r\n  UpdatedImage,\r\n  UploadImagesPlugin,\r\n  Youtube,\r\n} from \"novel\";\r\nimport { Markdown } from \"tiptap-markdown\";\r\nimport { Table } from \"@tiptap/extension-table\";\r\nimport { TableHeader } from \"@tiptap/extension-table-header\";\r\nimport { TableRow } from \"@tiptap/extension-table-row\";\r\nimport { TableCell } from \"@tiptap/extension-table-cell\";\r\nimport { cx } from \"class-variance-authority\";\r\nimport { common, createLowlight } from \"lowlight\";\r\n\r\n//TODO I am using cx here to get tailwind autocomplete working, idk if someone else can write a regex to just capture the class key in objects\r\nconst aiHighlight = AIHighlight;\r\n//You can overwrite the placeholder with your own configuration\r\nconst placeholder = Placeholder;\r\nconst tiptapLink = TiptapLink.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\r\n      \"text-muted-foreground underline underline-offset-[3px] hover:text-primary transition-colors cursor-pointer\",\r\n    ),\r\n  },\r\n});\r\n\r\nconst tiptapImage = TiptapImage.extend({\r\n  addProseMirrorPlugins() {\r\n    return [\r\n      UploadImagesPlugin({\r\n        imageClass: cx(\"opacity-40 rounded-lg border border-stone-200\"),\r\n      }),\r\n    ];\r\n  },\r\n}).configure({\r\n  allowBase64: true,\r\n  HTMLAttributes: {\r\n    class: cx(\"rounded-lg border border-muted\"),\r\n  },\r\n});\r\n\r\nconst updatedImage = UpdatedImage.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"rounded-lg border border-muted\"),\r\n  },\r\n});\r\n\r\nconst taskList = TaskList.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"not-prose pl-2 \"),\r\n  },\r\n});\r\nconst taskItem = TaskItem.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"flex gap-2 items-start my-4\"),\r\n  },\r\n  nested: true,\r\n});\r\n\r\nconst horizontalRule = HorizontalRule.configure({\r\n  HTMLAttributes: {},\r\n});\r\n\r\nconst starterKit = StarterKit.configure({\r\n  bulletList: {\r\n    HTMLAttributes: {},\r\n  },\r\n  orderedList: {\r\n    HTMLAttributes: {\r\n      class: cx(\"list-decimal list-outside leading-3 -mt-2\"),\r\n    },\r\n  },\r\n  listItem: {\r\n    HTMLAttributes: {},\r\n  },\r\n  blockquote: {\r\n    HTMLAttributes: {\r\n      class: cx(\"border-l-4 border-primary\"),\r\n    },\r\n  },\r\n  codeBlock: false,\r\n  code: {\r\n    HTMLAttributes: {\r\n      spellcheck: \"false\",\r\n    },\r\n  },\r\n  horizontalRule: false,\r\n  dropcursor: {\r\n    color: \"#DBEAFE\",\r\n    width: 4,\r\n  },\r\n  gapcursor: false,\r\n});\r\n\r\nconst codeBlockLowlight = CodeBlockLowlight.configure({\r\n  // configure lowlight: common /  all / use highlightJS in case there is a need to specify certain language grammars only\r\n  // common: covers 37 language grammars which should be good enough in most cases\r\n  lowlight: createLowlight(common),\r\n});\r\n\r\nconst youtube = Youtube.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"rounded-lg border border-muted\"),\r\n  },\r\n  inline: false,\r\n});\r\n\r\nconst twitter = Twitter.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"not-prose\"),\r\n  },\r\n  inline: false,\r\n});\r\n\r\nconst mathematics = Mathematics.configure({\r\n  HTMLAttributes: {\r\n    class: cx(\"text-foreground rounded p-1 hover:bg-accent cursor-pointer\"),\r\n  },\r\n  katexOptions: {\r\n    throwOnError: false,\r\n  },\r\n});\r\n\r\nconst characterCount = CharacterCount.configure();\r\n\r\nconst table = Table.configure();\r\nconst tableRow = TableRow.configure();\r\nconst tableCell = TableCell.configure();\r\nconst tableHeader = TableHeader.configure();\r\n\r\nconst markdownExtension = Markdown.configure({\r\n  html: true,\r\n  tightLists: true,\r\n  tightListClass: \"tight\",\r\n  bulletListMarker: \"-\",\r\n  linkify: false,\r\n  breaks: false,\r\n  transformPastedText: false,\r\n  transformCopiedText: false,\r\n});\r\n\r\nconst globalDragHandle = GlobalDragHandle.configure({});\r\n\r\nexport const defaultExtensions = [\r\n  starterKit,\r\n  placeholder,\r\n  tiptapLink,\r\n  updatedImage,\r\n  taskList,\r\n  taskItem,\r\n  table,\r\n  tableRow,\r\n  tableCell,\r\n  tableHeader,\r\n  horizontalRule,\r\n  aiHighlight,\r\n  codeBlockLowlight,\r\n  youtube,\r\n  twitter,\r\n  mathematics,\r\n  characterCount,\r\n  TiptapUnderline,\r\n  markdownExtension,\r\n  HighlightExtension,\r\n  TextStyle,\r\n  Color,\r\n  CustomKeymap,\r\n  globalDragHandle,\r\n];\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;;AAEA,8IAA8I;AAC9I,MAAM,cAAc,kQAAA,CAAA,cAAW;AAC/B,+DAA+D;AAC/D,MAAM,cAAc,kQAAA,CAAA,cAAW;AAC/B,MAAM,aAAa,iTAAA,CAAA,aAAU,CAAC,SAAS,CAAC;IACtC,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EACN;IAEJ;AACF;AAEA,MAAM,cAAc,mTAAA,CAAA,cAAW,CAAC,MAAM,CAAC;IACrC;QACE,OAAO;YACL,CAAA,GAAA,kQAAA,CAAA,qBAAkB,AAAD,EAAE;gBACjB,YAAY,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;YACjB;SACD;IACH;AACF,GAAG,SAAS,CAAC;IACX,aAAa;IACb,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;IACZ;AACF;AAEA,MAAM,eAAe,kQAAA,CAAA,eAAY,CAAC,SAAS,CAAC;IAC1C,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;IACZ;AACF;AAEA,MAAM,WAAW,6QAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;IAClC,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;IACZ;AACF;AACA,MAAM,WAAW,6QAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;IAClC,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;IACZ;IACA,QAAQ;AACV;AAEA,MAAM,iBAAiB,kQAAA,CAAA,iBAAc,CAAC,SAAS,CAAC;IAC9C,gBAAgB,CAAC;AACnB;AAEA,MAAM,aAAa,+QAAA,CAAA,aAAU,CAAC,SAAS,CAAC;IACtC,YAAY;QACV,gBAAgB,CAAC;IACnB;IACA,aAAa;QACX,gBAAgB;YACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;QACZ;IACF;IACA,UAAU;QACR,gBAAgB,CAAC;IACnB;IACA,YAAY;QACV,gBAAgB;YACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;QACZ;IACF;IACA,WAAW;IACX,MAAM;QACJ,gBAAgB;YACd,YAAY;QACd;IACF;IACA,gBAAgB;IAChB,YAAY;QACV,OAAO;QACP,OAAO;IACT;IACA,WAAW;AACb;AAEA,MAAM,oBAAoB,0UAAA,CAAA,oBAAiB,CAAC,SAAS,CAAC;IACpD,wHAAwH;IACxH,gFAAgF;IAChF,UAAU,CAAA,GAAA,gMAAA,CAAA,iBAAc,AAAD,EAAE,uOAAA,CAAA,SAAM;AACjC;AAEA,MAAM,UAAU,8SAAA,CAAA,UAAO,CAAC,SAAS,CAAC;IAChC,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;IACZ;IACA,QAAQ;AACV;AAEA,MAAM,UAAU,kQAAA,CAAA,UAAO,CAAC,SAAS,CAAC;IAChC,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;IACZ;IACA,QAAQ;AACV;AAEA,MAAM,cAAc,kQAAA,CAAA,cAAW,CAAC,SAAS,CAAC;IACxC,gBAAgB;QACd,OAAO,CAAA,GAAA,8OAAA,CAAA,KAAE,AAAD,EAAE;IACZ;IACA,cAAc;QACZ,cAAc;IAChB;AACF;AAEA,MAAM,iBAAiB,6TAAA,CAAA,iBAAc,CAAC,SAAS;AAE/C,MAAM,QAAQ,yQAAA,CAAA,QAAK,CAAC,SAAS;AAC7B,MAAM,WAAW,6QAAA,CAAA,WAAQ,CAAC,SAAS;AACnC,MAAM,YAAY,8QAAA,CAAA,YAAS,CAAC,SAAS;AACrC,MAAM,cAAc,gRAAA,CAAA,cAAW,CAAC,SAAS;AAEzC,MAAM,oBAAoB,kRAAA,CAAA,WAAQ,CAAC,SAAS,CAAC;IAC3C,MAAM;IACN,YAAY;IACZ,gBAAgB;IAChB,kBAAkB;IAClB,SAAS;IACT,QAAQ;IACR,qBAAqB;IACrB,qBAAqB;AACvB;AAEA,MAAM,mBAAmB,+TAAA,CAAA,mBAAgB,CAAC,SAAS,CAAC,CAAC;AAE9C,MAAM,oBAAoB;IAC/B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,qTAAA,CAAA,kBAAe;IACf;IACA,kQAAA,CAAA,qBAAkB;IAClB,sTAAA,CAAA,YAAS;IACT,yQAAA,CAAA,QAAK;IACL,kQAAA,CAAA,eAAY;IACZ;CACD", "debugId": null}}, {"offset": {"line": 4733, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,sSAAC,gRAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,sSAAC,gRAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,sSAAC,gRAAA,CAAA,SAAuB;kBACtB,cAAA,sSAAC,gRAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,sSAAC,gRAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 4814, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/editor/selectors/color-selector.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Check, ChevronDown } from \"lucide-react\";\r\nimport { EditorBubbleItem, useEditor } from \"novel\";\r\n\r\nimport { Button } from \"../../ui/button\";\r\nimport { Popover, PopoverContent, PopoverTrigger } from \"../../ui/popover\";\r\nexport interface BubbleColorMenuItem {\r\n  name: string;\r\n  color: string;\r\n}\r\n\r\nconst TEXT_COLORS: BubbleColorMenuItem[] = [\r\n  {\r\n    name: \"Default\",\r\n    color: \"var(--novel-black)\",\r\n  },\r\n  {\r\n    name: \"Purple\",\r\n    color: \"#9333EA\",\r\n  },\r\n  {\r\n    name: \"Red\",\r\n    color: \"#E00000\",\r\n  },\r\n  {\r\n    name: \"Yellow\",\r\n    color: \"#EAB308\",\r\n  },\r\n  {\r\n    name: \"Blue\",\r\n    color: \"#2563EB\",\r\n  },\r\n  {\r\n    name: \"Green\",\r\n    color: \"#008A00\",\r\n  },\r\n  {\r\n    name: \"Orange\",\r\n    color: \"#FFA500\",\r\n  },\r\n  {\r\n    name: \"<PERSON>\",\r\n    color: \"#BA4081\",\r\n  },\r\n  {\r\n    name: \"<PERSON>\",\r\n    color: \"#A8A29E\",\r\n  },\r\n];\r\n\r\nconst HIGHLIGHT_COLORS: BubbleColorMenuItem[] = [\r\n  {\r\n    name: \"Default\",\r\n    color: \"var(--novel-highlight-default)\",\r\n  },\r\n  {\r\n    name: \"Purple\",\r\n    color: \"var(--novel-highlight-purple)\",\r\n  },\r\n  {\r\n    name: \"Red\",\r\n    color: \"var(--novel-highlight-red)\",\r\n  },\r\n  {\r\n    name: \"Yellow\",\r\n    color: \"var(--novel-highlight-yellow)\",\r\n  },\r\n  {\r\n    name: \"Blue\",\r\n    color: \"var(--novel-highlight-blue)\",\r\n  },\r\n  {\r\n    name: \"Green\",\r\n    color: \"var(--novel-highlight-green)\",\r\n  },\r\n  {\r\n    name: \"Orange\",\r\n    color: \"var(--novel-highlight-orange)\",\r\n  },\r\n  {\r\n    name: \"Pink\",\r\n    color: \"var(--novel-highlight-pink)\",\r\n  },\r\n  {\r\n    name: \"Gray\",\r\n    color: \"var(--novel-highlight-gray)\",\r\n  },\r\n];\r\n\r\ninterface ColorSelectorProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n}\r\n\r\nexport const ColorSelector = ({ open, onOpenChange }: ColorSelectorProps) => {\r\n  const { editor } = useEditor();\r\n\r\n  if (!editor) return null;\r\n  const activeColorItem = TEXT_COLORS.find(({ color }) =>\r\n    editor.isActive(\"textStyle\", { color }),\r\n  );\r\n\r\n  const activeHighlightItem = HIGHLIGHT_COLORS.find(({ color }) =>\r\n    editor.isActive(\"highlight\", { color }),\r\n  );\r\n\r\n  return (\r\n    <Popover modal={true} open={open} onOpenChange={onOpenChange}>\r\n      <PopoverTrigger asChild>\r\n        <Button size=\"sm\" className=\"gap-2 rounded-none\" variant=\"ghost\">\r\n          <span\r\n            className=\"rounded-sm px-1\"\r\n            style={{\r\n              color: activeColorItem?.color,\r\n              backgroundColor: activeHighlightItem?.color,\r\n            }}\r\n          >\r\n            A\r\n          </span>\r\n          <ChevronDown className=\"h-4 w-4\" />\r\n        </Button>\r\n      </PopoverTrigger>\r\n\r\n      <PopoverContent\r\n        sideOffset={5}\r\n        className=\"my-1 flex max-h-80 w-48 flex-col overflow-hidden overflow-y-auto rounded border p-1 shadow-xl\"\r\n        align=\"start\"\r\n      >\r\n        <div className=\"flex flex-col\">\r\n          <div className=\"text-muted-foreground my-1 px-2 text-sm font-semibold\">\r\n            Color\r\n          </div>\r\n          {TEXT_COLORS.map(({ name, color }) => (\r\n            <EditorBubbleItem\r\n              key={name}\r\n              onSelect={() => {\r\n                editor.commands.unsetColor();\r\n                name !== \"Default\" &&\r\n                  editor\r\n                    .chain()\r\n                    .focus()\r\n                    .setColor(color || \"\")\r\n                    .run();\r\n                onOpenChange(false);\r\n              }}\r\n              className=\"hover:bg-accent flex cursor-pointer items-center justify-between px-2 py-1 text-sm\"\r\n            >\r\n              <div className=\"flex items-center gap-2\">\r\n                <div\r\n                  className=\"rounded-sm border px-2 py-px font-medium\"\r\n                  style={{ color }}\r\n                >\r\n                  A\r\n                </div>\r\n                <span>{name}</span>\r\n              </div>\r\n            </EditorBubbleItem>\r\n          ))}\r\n        </div>\r\n        <div>\r\n          <div className=\"text-muted-foreground my-1 px-2 text-sm font-semibold\">\r\n            Background\r\n          </div>\r\n          {HIGHLIGHT_COLORS.map(({ name, color }) => (\r\n            <EditorBubbleItem\r\n              key={name}\r\n              onSelect={() => {\r\n                editor.commands.unsetHighlight();\r\n                name !== \"Default\" &&\r\n                  editor.chain().focus().setHighlight({ color }).run();\r\n                onOpenChange(false);\r\n              }}\r\n              className=\"hover:bg-accent flex cursor-pointer items-center justify-between px-2 py-1 text-sm\"\r\n            >\r\n              <div className=\"flex items-center gap-2\">\r\n                <div\r\n                  className=\"rounded-sm border px-2 py-px font-medium\"\r\n                  style={{ backgroundColor: color }}\r\n                >\r\n                  A\r\n                </div>\r\n                <span>{name}</span>\r\n              </div>\r\n              {editor.isActive(\"highlight\", { color }) && (\r\n                <Check className=\"h-4 w-4\" />\r\n              )}\r\n            </EditorBubbleItem>\r\n          ))}\r\n        </div>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AACA;AAAA;AAEA;AACA;;;;;;;AAMA,MAAM,cAAqC;IACzC;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;CACD;AAED,MAAM,mBAA0C;IAC9C;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;IACT;CACD;AAOM,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,YAAY,EAAsB;;IACtE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAE3B,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,kBAAkB,YAAY,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GACjD,OAAO,QAAQ,CAAC,aAAa;YAAE;QAAM;IAGvC,MAAM,sBAAsB,iBAAiB,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,GAC1D,OAAO,QAAQ,CAAC,aAAa;YAAE;QAAM;IAGvC,qBACE,sSAAC,sIAAA,CAAA,UAAO;QAAC,OAAO;QAAM,MAAM;QAAM,cAAc;;0BAC9C,sSAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,sSAAC,qIAAA,CAAA,SAAM;oBAAC,MAAK;oBAAK,WAAU;oBAAqB,SAAQ;;sCACvD,sSAAC;4BACC,WAAU;4BACV,OAAO;gCACL,OAAO,iBAAiB;gCACxB,iBAAiB,qBAAqB;4BACxC;sCACD;;;;;;sCAGD,sSAAC,2SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAI3B,sSAAC,sIAAA,CAAA,iBAAc;gBACb,YAAY;gBACZ,WAAU;gBACV,OAAM;;kCAEN,sSAAC;wBAAI,WAAU;;0CACb,sSAAC;gCAAI,WAAU;0CAAwD;;;;;;4BAGtE,YAAY,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,iBAC/B,sSAAC,kQAAA,CAAA,mBAAgB;oCAEf,UAAU;wCACR,OAAO,QAAQ,CAAC,UAAU;wCAC1B,SAAS,aACP,OACG,KAAK,GACL,KAAK,GACL,QAAQ,CAAC,SAAS,IAClB,GAAG;wCACR,aAAa;oCACf;oCACA,WAAU;8CAEV,cAAA,sSAAC;wCAAI,WAAU;;0DACb,sSAAC;gDACC,WAAU;gDACV,OAAO;oDAAE;gDAAM;0DAChB;;;;;;0DAGD,sSAAC;0DAAM;;;;;;;;;;;;mCApBJ;;;;;;;;;;;kCAyBX,sSAAC;;0CACC,sSAAC;gCAAI,WAAU;0CAAwD;;;;;;4BAGtE,iBAAiB,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,iBACpC,sSAAC,kQAAA,CAAA,mBAAgB;oCAEf,UAAU;wCACR,OAAO,QAAQ,CAAC,cAAc;wCAC9B,SAAS,aACP,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,CAAC;4CAAE;wCAAM,GAAG,GAAG;wCACpD,aAAa;oCACf;oCACA,WAAU;;sDAEV,sSAAC;4CAAI,WAAU;;8DACb,sSAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,iBAAiB;oDAAM;8DACjC;;;;;;8DAGD,sSAAC;8DAAM;;;;;;;;;;;;wCAER,OAAO,QAAQ,CAAC,aAAa;4CAAE;wCAAM,oBACpC,sSAAC,2RAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;mCAnBd;;;;;;;;;;;;;;;;;;;;;;;AA2BnB;GAlGa;;QACQ,gUAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 5119, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/editor/selectors/link-selector.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Button } from \"../../ui/button\";\r\nimport { PopoverContent } from \"../../ui/popover\";\r\nimport { cn } from \"../../../lib/utils\";\r\nimport { Popover, PopoverTrigger } from \"@radix-ui/react-popover\";\r\nimport { Check, Trash } from \"lucide-react\";\r\nimport { useEditor } from \"novel\";\r\nimport { useEffect, useRef } from \"react\";\r\n\r\nexport function isValidUrl(url: string) {\r\n  try {\r\n    new URL(url);\r\n    return true;\r\n  } catch (_e) {\r\n    return false;\r\n  }\r\n}\r\nexport function getUrlFromString(str: string) {\r\n  if (isValidUrl(str)) return str;\r\n  try {\r\n    if (str.includes(\".\") && !str.includes(\" \")) {\r\n      return new URL(`https://${str}`).toString();\r\n    }\r\n  } catch (_e) {\r\n    return null;\r\n  }\r\n}\r\ninterface LinkSelectorProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n}\r\n\r\nexport const LinkSelector = ({ open, onOpenChange }: LinkSelectorProps) => {\r\n  const inputRef = useRef<HTMLInputElement>(null);\r\n  const { editor } = useEditor();\r\n\r\n  // Autofocus on input by default\r\n  useEffect(() => {\r\n    inputRef.current?.focus();\r\n  });\r\n  if (!editor) return null;\r\n\r\n  return (\r\n    <Popover modal={true} open={open} onOpenChange={onOpenChange}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          size=\"sm\"\r\n          variant=\"ghost\"\r\n          className=\"gap-2 rounded-none border-none\"\r\n        >\r\n          <p className=\"text-base\">↗</p>\r\n          <p\r\n            className={cn(\"underline decoration-stone-400 underline-offset-4\", {\r\n              \"text-blue-500\": editor.isActive(\"link\"),\r\n            })}\r\n          >\r\n            Link\r\n          </p>\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent align=\"start\" className=\"w-60 p-0\" sideOffset={10}>\r\n        <form\r\n          onSubmit={(e) => {\r\n            const target = e.currentTarget as HTMLFormElement;\r\n            e.preventDefault();\r\n            const input = target[0] as HTMLInputElement;\r\n            const url = getUrlFromString(input.value);\r\n            if (url) {\r\n              editor.chain().focus().setLink({ href: url }).run();\r\n              onOpenChange(false);\r\n            }\r\n          }}\r\n          className=\"flex p-1\"\r\n        >\r\n          <input\r\n            ref={inputRef}\r\n            type=\"text\"\r\n            placeholder=\"Paste a link\"\r\n            className=\"bg-background flex-1 p-1 text-sm outline-none\"\r\n            defaultValue={editor.getAttributes(\"link\").href || \"\"}\r\n          />\r\n          {editor.getAttributes(\"link\").href ? (\r\n            <Button\r\n              size=\"icon\"\r\n              variant=\"outline\"\r\n              type=\"button\"\r\n              className=\"flex h-8 items-center rounded-sm p-1 text-red-600 transition-all hover:bg-red-100 dark:hover:bg-red-800\"\r\n              onClick={() => {\r\n                editor.chain().focus().unsetLink().run();\r\n                if (inputRef.current) inputRef.current.value = \"\";\r\n                onOpenChange(false);\r\n              }}\r\n            >\r\n              <Trash className=\"h-4 w-4\" />\r\n            </Button>\r\n          ) : (\r\n            <Button size=\"icon\" className=\"h-8\">\r\n              <Check className=\"h-4 w-4\" />\r\n            </Button>\r\n          )}\r\n        </form>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;;AAE/B;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;;;;;;;;;;AAEO,SAAS,WAAW,GAAW;IACpC,IAAI;QACF,IAAI,IAAI;QACR,OAAO;IACT,EAAE,OAAO,IAAI;QACX,OAAO;IACT;AACF;AACO,SAAS,iBAAiB,GAAW;IAC1C,IAAI,WAAW,MAAM,OAAO;IAC5B,IAAI;QACF,IAAI,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM;YAC3C,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ;QAC3C;IACF,EAAE,OAAO,IAAI;QACX,OAAO;IACT;AACF;AAMO,MAAM,eAAe,CAAC,EAAE,IAAI,EAAE,YAAY,EAAqB;;IACpE,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAE3B,gCAAgC;IAChC,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;kCAAE;YACR,SAAS,OAAO,EAAE;QACpB;;IACA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,sSAAC,gRAAA,CAAA,UAAO;QAAC,OAAO;QAAM,MAAM;QAAM,cAAc;;0BAC9C,sSAAC,gRAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,sSAAC,qIAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAQ;oBACR,WAAU;;sCAEV,sSAAC;4BAAE,WAAU;sCAAY;;;;;;sCACzB,sSAAC;4BACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;gCACjE,iBAAiB,OAAO,QAAQ,CAAC;4BACnC;sCACD;;;;;;;;;;;;;;;;;0BAKL,sSAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAM;gBAAQ,WAAU;gBAAW,YAAY;0BAC7D,cAAA,sSAAC;oBACC,UAAU,CAAC;wBACT,MAAM,SAAS,EAAE,aAAa;wBAC9B,EAAE,cAAc;wBAChB,MAAM,QAAQ,MAAM,CAAC,EAAE;wBACvB,MAAM,MAAM,iBAAiB,MAAM,KAAK;wBACxC,IAAI,KAAK;4BACP,OAAO,KAAK,GAAG,KAAK,GAAG,OAAO,CAAC;gCAAE,MAAM;4BAAI,GAAG,GAAG;4BACjD,aAAa;wBACf;oBACF;oBACA,WAAU;;sCAEV,sSAAC;4BACC,KAAK;4BACL,MAAK;4BACL,aAAY;4BACZ,WAAU;4BACV,cAAc,OAAO,aAAa,CAAC,QAAQ,IAAI,IAAI;;;;;;wBAEpD,OAAO,aAAa,CAAC,QAAQ,IAAI,iBAChC,sSAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;gCACP,OAAO,KAAK,GAAG,KAAK,GAAG,SAAS,GAAG,GAAG;gCACtC,IAAI,SAAS,OAAO,EAAE,SAAS,OAAO,CAAC,KAAK,GAAG;gCAC/C,aAAa;4BACf;sCAEA,cAAA,sSAAC,2RAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;iDAGnB,sSAAC,qIAAA,CAAA,SAAM;4BAAC,MAAK;4BAAO,WAAU;sCAC5B,cAAA,sSAAC,2RAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/B;GAxEa;;QAEQ,gUAAA,CAAA,YAAS;;;KAFjB", "debugId": null}}, {"offset": {"line": 5315, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/editor/selectors/math-selector.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Button } from \"../../ui/button\";\r\nimport { cn } from \"../../../lib/utils\";\r\nimport { SigmaIcon } from \"lucide-react\";\r\nimport { useEditor } from \"novel\";\r\n\r\nexport const MathSelector = () => {\r\n  const { editor } = useEditor();\r\n\r\n  if (!editor) return null;\r\n\r\n  return (\r\n    <Button\r\n      variant=\"ghost\"\r\n      size=\"sm\"\r\n      className=\"w-12 rounded-none\"\r\n      onClick={(evt) => {\r\n        if (editor.isActive(\"math\")) {\r\n          editor.chain().focus().unsetLatex().run();\r\n        } else {\r\n          const { from, to } = editor.state.selection;\r\n          const latex = editor.state.doc.textBetween(from, to);\r\n\r\n          if (!latex) return;\r\n\r\n          editor.chain().focus().setLatex({ latex }).run();\r\n        }\r\n      }}\r\n    >\r\n      <SigmaIcon\r\n        className={cn(\"size-4\", { \"text-blue-500\": editor.isActive(\"math\") })}\r\n        strokeWidth={2.3}\r\n      />\r\n    </Button>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AACA;AACA;;;;;;;AAEO,MAAM,eAAe;;IAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAE3B,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,sSAAC,qIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,WAAU;QACV,SAAS,CAAC;YACR,IAAI,OAAO,QAAQ,CAAC,SAAS;gBAC3B,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;YACzC,OAAO;gBACL,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,OAAO,KAAK,CAAC,SAAS;gBAC3C,MAAM,QAAQ,OAAO,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM;gBAEjD,IAAI,CAAC,OAAO;gBAEZ,OAAO,KAAK,GAAG,KAAK,GAAG,QAAQ,CAAC;oBAAE;gBAAM,GAAG,GAAG;YAChD;QACF;kBAEA,cAAA,sSAAC,+RAAA,CAAA,YAAS;YACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;gBAAE,iBAAiB,OAAO,QAAQ,CAAC;YAAQ;YACnE,aAAa;;;;;;;;;;;AAIrB;GA7Ba;;QACQ,gUAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 5384, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/editor/selectors/node-selector.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport {\r\n  Check,\r\n  CheckSquare,\r\n  ChevronDown,\r\n  Code,\r\n  Heading1,\r\n  Heading2,\r\n  Heading3,\r\n  ListOrdered,\r\n  type LucideIcon,\r\n  TextIcon,\r\n  TextQuote,\r\n} from \"lucide-react\";\r\nimport { EditorBubbleItem, useEditor } from \"novel\";\r\n\r\nimport { Button } from \"../../ui/button\";\r\nimport { PopoverContent, PopoverTrigger } from \"../../ui/popover\";\r\nimport { Popover } from \"@radix-ui/react-popover\";\r\n\r\nexport type SelectorItem = {\r\n  name: string;\r\n  icon: LucideIcon;\r\n  command: (\r\n    editor: NonNullable<ReturnType<typeof useEditor>[\"editor\"]>,\r\n  ) => void;\r\n  isActive: (\r\n    editor: NonNullable<ReturnType<typeof useEditor>[\"editor\"]>,\r\n  ) => boolean;\r\n};\r\n\r\nconst items: SelectorItem[] = [\r\n  {\r\n    name: \"Text\",\r\n    icon: TextIcon,\r\n    command: (editor) => editor.chain().focus().clearNodes().run(),\r\n    // I feel like there has to be a more efficient way to do this – feel free to PR if you know how!\r\n    isActive: (editor) =>\r\n      editor.isActive(\"paragraph\") &&\r\n      !editor.isActive(\"bulletList\") &&\r\n      !editor.isActive(\"orderedList\"),\r\n  },\r\n  {\r\n    name: \"Heading 1\",\r\n    icon: Heading1,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleHeading({ level: 1 }).run(),\r\n    isActive: (editor) => editor.isActive(\"heading\", { level: 1 }),\r\n  },\r\n  {\r\n    name: \"Heading 2\",\r\n    icon: Heading2,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleHeading({ level: 2 }).run(),\r\n    isActive: (editor) => editor.isActive(\"heading\", { level: 2 }),\r\n  },\r\n  {\r\n    name: \"Heading 3\",\r\n    icon: Heading3,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleHeading({ level: 3 }).run(),\r\n    isActive: (editor) => editor.isActive(\"heading\", { level: 3 }),\r\n  },\r\n  {\r\n    name: \"To-do List\",\r\n    icon: CheckSquare,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleTaskList().run(),\r\n    isActive: (editor) => editor.isActive(\"taskItem\"),\r\n  },\r\n  {\r\n    name: \"Bullet List\",\r\n    icon: ListOrdered,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleBulletList().run(),\r\n    isActive: (editor) => editor.isActive(\"bulletList\"),\r\n  },\r\n  {\r\n    name: \"Numbered List\",\r\n    icon: ListOrdered,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleOrderedList().run(),\r\n    isActive: (editor) => editor.isActive(\"orderedList\"),\r\n  },\r\n  {\r\n    name: \"Quote\",\r\n    icon: TextQuote,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleBlockquote().run(),\r\n    isActive: (editor) => editor.isActive(\"blockquote\"),\r\n  },\r\n  {\r\n    name: \"Code\",\r\n    icon: Code,\r\n    command: (editor) =>\r\n      editor.chain().focus().clearNodes().toggleCodeBlock().run(),\r\n    isActive: (editor) => editor.isActive(\"codeBlock\"),\r\n  },\r\n];\r\ninterface NodeSelectorProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n}\r\n\r\nexport const NodeSelector = ({ open, onOpenChange }: NodeSelectorProps) => {\r\n  const { editor } = useEditor();\r\n  if (!editor) return null;\r\n  const activeItem = items.filter((item) => item.isActive(editor)).pop() ?? {\r\n    name: \"Multiple\",\r\n  };\r\n\r\n  return (\r\n    <Popover modal={true} open={open} onOpenChange={onOpenChange}>\r\n      <PopoverTrigger\r\n        asChild\r\n        className=\"hover:bg-accent gap-2 rounded-none border-none focus:ring-0\"\r\n      >\r\n        <Button size=\"sm\" variant=\"ghost\" className=\"gap-2\">\r\n          <span className=\"text-sm whitespace-nowrap\">{activeItem.name}</span>\r\n          <ChevronDown className=\"h-4 w-4\" />\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent sideOffset={5} align=\"start\" className=\"w-48 p-1\">\r\n        {items.map((item) => (\r\n          <EditorBubbleItem\r\n            key={item.name}\r\n            onSelect={(editor) => {\r\n              item.command(editor);\r\n              onOpenChange(false);\r\n            }}\r\n            className=\"hover:bg-accent flex cursor-pointer items-center justify-between rounded-sm px-2 py-1 text-sm\"\r\n          >\r\n            <div className=\"flex items-center space-x-2\">\r\n              <div className=\"rounded-sm border p-1\">\r\n                <item.icon className=\"h-3 w-3\" />\r\n              </div>\r\n              <span>{item.name}</span>\r\n            </div>\r\n            {activeItem.name === item.name && <Check className=\"h-4 w-4\" />}\r\n          </EditorBubbleItem>\r\n        ))}\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAAA;AAEA;AACA;AACA;;;;;;;;AAaA,MAAM,QAAwB;IAC5B;QACE,MAAM;QACN,MAAM,6RAAA,CAAA,WAAQ;QACd,SAAS,CAAC,SAAW,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;QAC5D,iGAAiG;QACjG,UAAU,CAAC,SACT,OAAO,QAAQ,CAAC,gBAChB,CAAC,OAAO,QAAQ,CAAC,iBACjB,CAAC,OAAO,QAAQ,CAAC;IACrB;IACA;QACE,MAAM;QACN,MAAM,qSAAA,CAAA,WAAQ;QACd,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,aAAa,CAAC;gBAAE,OAAO;YAAE,GAAG,GAAG;QACrE,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC,WAAW;gBAAE,OAAO;YAAE;IAC9D;IACA;QACE,MAAM;QACN,MAAM,qSAAA,CAAA,WAAQ;QACd,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,aAAa,CAAC;gBAAE,OAAO;YAAE,GAAG,GAAG;QACrE,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC,WAAW;gBAAE,OAAO;YAAE;IAC9D;IACA;QACE,MAAM;QACN,MAAM,qSAAA,CAAA,WAAQ;QACd,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,aAAa,CAAC;gBAAE,OAAO;YAAE,GAAG,GAAG;QACrE,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC,WAAW;gBAAE,OAAO;YAAE;IAC9D;IACA;QACE,MAAM;QACN,MAAM,kTAAA,CAAA,cAAW;QACjB,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,cAAc,GAAG,GAAG;QAC1D,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;IACxC;IACA;QACE,MAAM;QACN,MAAM,2SAAA,CAAA,cAAW;QACjB,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,gBAAgB,GAAG,GAAG;QAC5D,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;IACxC;IACA;QACE,MAAM;QACN,MAAM,2SAAA,CAAA,cAAW;QACjB,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,iBAAiB,GAAG,GAAG;QAC7D,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;IACxC;IACA;QACE,MAAM;QACN,MAAM,uSAAA,CAAA,YAAS;QACf,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,gBAAgB,GAAG,GAAG;QAC5D,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;IACxC;IACA;QACE,MAAM;QACN,MAAM,yRAAA,CAAA,OAAI;QACV,SAAS,CAAC,SACR,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,eAAe,GAAG,GAAG;QAC3D,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;IACxC;CACD;AAMM,MAAM,eAAe,CAAC,EAAE,IAAI,EAAE,YAAY,EAAqB;;IACpE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAC3B,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,CAAC,SAAS,GAAG,MAAM;QACxE,MAAM;IACR;IAEA,qBACE,sSAAC,gRAAA,CAAA,UAAO;QAAC,OAAO;QAAM,MAAM;QAAM,cAAc;;0BAC9C,sSAAC,sIAAA,CAAA,iBAAc;gBACb,OAAO;gBACP,WAAU;0BAEV,cAAA,sSAAC,qIAAA,CAAA,SAAM;oBAAC,MAAK;oBAAK,SAAQ;oBAAQ,WAAU;;sCAC1C,sSAAC;4BAAK,WAAU;sCAA6B,WAAW,IAAI;;;;;;sCAC5D,sSAAC,2SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAG3B,sSAAC,sIAAA,CAAA,iBAAc;gBAAC,YAAY;gBAAG,OAAM;gBAAQ,WAAU;0BACpD,MAAM,GAAG,CAAC,CAAC,qBACV,sSAAC,kQAAA,CAAA,mBAAgB;wBAEf,UAAU,CAAC;4BACT,KAAK,OAAO,CAAC;4BACb,aAAa;wBACf;wBACA,WAAU;;0CAEV,sSAAC;gCAAI,WAAU;;kDACb,sSAAC;wCAAI,WAAU;kDACb,cAAA,sSAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAEvB,sSAAC;kDAAM,KAAK,IAAI;;;;;;;;;;;;4BAEjB,WAAW,IAAI,KAAK,KAAK,IAAI,kBAAI,sSAAC,2RAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;uBAb9C,KAAK,IAAI;;;;;;;;;;;;;;;;AAmB1B;GAxCa;;QACQ,gUAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 5610, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,sSAAC,+QAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 5646, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/icons/magic.tsx"], "sourcesContent": ["export default function Magic({ className }: { className: string }) {\r\n  return (\r\n    <svg\r\n      width=\"469\"\r\n      height=\"469\"\r\n      viewBox=\"0 0 469 469\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      shapeRendering=\"geometricPrecision\"\r\n      stroke=\"currentColor\"\r\n      strokeLinecap=\"round\"\r\n      strokeLinejoin=\"round\"\r\n      strokeWidth=\"1.5\"\r\n      className={className}\r\n    >\r\n      <title>Magic AI icon</title>\r\n\r\n      <path\r\n        d=\"M237.092 62.3004L266.754 71.4198C267.156 71.5285 267.51 71.765 267.765 72.0934C268.02 72.4218 268.161 72.8243 268.166 73.2399C268.172 73.6555 268.042 74.0616 267.796 74.3967C267.55 74.7318 267.201 74.9777 266.803 75.097L237.141 84.3145C236.84 84.4058 236.566 84.5699 236.344 84.7922C236.121 85.0146 235.957 85.2883 235.866 85.5893L226.747 115.252C226.638 115.653 226.401 116.008 226.073 116.263C225.745 116.517 225.342 116.658 224.926 116.664C224.511 116.669 224.105 116.539 223.77 116.293C223.435 116.047 223.189 115.699 223.069 115.301L213.852 85.6383C213.761 85.3374 213.597 85.0636 213.374 84.8412C213.152 84.6189 212.878 84.4548 212.577 84.3635L182.914 75.2441C182.513 75.1354 182.158 74.8989 181.904 74.5705C181.649 74.2421 181.508 73.8396 181.503 73.424C181.497 73.0084 181.627 72.6023 181.873 72.2672C182.119 71.9321 182.467 71.6863 182.865 71.5669L212.528 62.3494C212.829 62.2582 213.103 62.0941 213.325 61.8717C213.547 61.6494 213.712 61.3756 213.803 61.0747L222.922 31.4121C223.031 31.0109 223.267 30.656 223.596 30.4013C223.924 30.1465 224.327 30.0057 224.742 30.0002C225.158 29.9946 225.564 30.1247 225.899 30.3706C226.234 30.6165 226.48 30.9649 226.599 31.363L235.817 61.0257C235.908 61.3266 236.072 61.6003 236.295 61.8227C236.517 62.0451 236.791 62.2091 237.092 62.3004Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n      <path\r\n        d=\"M155.948 155.848L202.771 168.939C203.449 169.131 204.045 169.539 204.47 170.101C204.895 170.663 205.125 171.348 205.125 172.052C205.125 172.757 204.895 173.442 204.47 174.004C204.045 174.566 203.449 174.974 202.771 175.166L155.899 188.06C155.361 188.209 154.87 188.496 154.475 188.891C154.079 189.286 153.793 189.777 153.644 190.316L140.553 237.138C140.361 237.816 139.953 238.413 139.391 238.838C138.829 239.262 138.144 239.492 137.44 239.492C136.735 239.492 136.05 239.262 135.488 238.838C134.927 238.413 134.519 237.816 134.327 237.138L121.432 190.267C121.283 189.728 120.997 189.237 120.601 188.842C120.206 188.446 119.715 188.16 119.177 188.011L72.3537 174.92C71.676 174.728 71.0795 174.32 70.6547 173.759C70.2299 173.197 70 172.512 70 171.807C70 171.103 70.2299 170.418 70.6547 169.856C71.0795 169.294 71.676 168.886 72.3537 168.694L119.226 155.799C119.764 155.65 120.255 155.364 120.65 154.969C121.046 154.573 121.332 154.082 121.481 153.544L134.572 106.721C134.764 106.043 135.172 105.447 135.734 105.022C136.295 104.597 136.981 104.367 137.685 104.367C138.389 104.367 139.075 104.597 139.637 105.022C140.198 105.447 140.606 106.043 140.798 106.721L153.693 153.593C153.842 154.131 154.128 154.622 154.524 155.018C154.919 155.413 155.41 155.699 155.948 155.848Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n      <path\r\n        d=\"M386.827 289.992C404.33 292.149 403.84 305.828 386.876 307.299C346.623 310.829 298.869 316.271 282.199 360.005C274.844 379.192 269.942 403.2 267.49 432.029C267.427 432.846 267.211 433.626 266.856 434.319C266.501 435.012 266.015 435.602 265.431 436.05C254.988 444.041 251.212 434.186 250.183 425.606C239.2 332.353 214.588 316.909 124.668 306.122C123.892 306.031 123.151 305.767 122.504 305.35C121.857 304.933 121.322 304.375 120.942 303.72C116.399 295.679 119.324 291.038 129.718 289.796C224.688 278.47 236.062 262.83 250.183 169.331C252.177 156.355 257.259 154.083 265.431 162.516C266.51 163.593 267.202 165.099 267.392 166.782C279.257 258.564 293.328 278.617 386.827 289.992Z\"\r\n        fill=\"currentColor\"\r\n      />\r\n    </svg>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS,MAAM,EAAE,SAAS,EAAyB;IAChE,qBACE,sSAAC;QACC,OAAM;QACN,QAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;QACN,gBAAe;QACf,QAAO;QACP,eAAc;QACd,gBAAe;QACf,aAAY;QACZ,WAAW;;0BAEX,sSAAC;0BAAM;;;;;;0BAEP,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;0BAEP,sSAAC;gBACC,GAAE;gBACF,MAAK;;;;;;;;;;;;AAIb;KA/BwB", "debugId": null}}, {"offset": {"line": 5715, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/ui/command.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Command as CommandPrimitive } from \"cmdk\"\r\nimport { SearchIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"~/lib/utils\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"~/components/ui/dialog\"\r\n\r\nfunction Command({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive>) {\r\n  return (\r\n    <CommandPrimitive\r\n      data-slot=\"command\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandDialog({\r\n  title = \"Command Palette\",\r\n  description = \"Search for a command to run...\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof Dialog> & {\r\n  title?: string\r\n  description?: string\r\n}) {\r\n  return (\r\n    <Dialog {...props}>\r\n      <DialogHeader className=\"sr-only\">\r\n        <DialogTitle>{title}</DialogTitle>\r\n        <DialogDescription>{description}</DialogDescription>\r\n      </DialogHeader>\r\n      <DialogContent className=\"overflow-hidden p-0\">\r\n        <Command className=\"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\r\n          {children}\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nfunction CommandInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Input>) {\r\n  return (\r\n    <div\r\n      data-slot=\"command-input-wrapper\"\r\n      className=\"flex h-9 items-center gap-2 border-b px-3\"\r\n    >\r\n      <SearchIcon className=\"size-4 shrink-0 opacity-50\" />\r\n      <CommandPrimitive.Input\r\n        data-slot=\"command-input\"\r\n        className={cn(\r\n          \"placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction CommandList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.List>) {\r\n  return (\r\n    <CommandPrimitive.List\r\n      data-slot=\"command-list\"\r\n      className={cn(\r\n        \"max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandEmpty({\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Empty>) {\r\n  return (\r\n    <CommandPrimitive.Empty\r\n      data-slot=\"command-empty\"\r\n      className=\"py-6 text-center text-sm\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandGroup({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Group>) {\r\n  return (\r\n    <CommandPrimitive.Group\r\n      data-slot=\"command-group\"\r\n      className={cn(\r\n        \"text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Separator>) {\r\n  return (\r\n    <CommandPrimitive.Separator\r\n      data-slot=\"command-separator\"\r\n      className={cn(\"bg-border -mx-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Item>) {\r\n  return (\r\n    <CommandPrimitive.Item\r\n      data-slot=\"command-item\"\r\n      className={cn(\r\n        \"data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"command-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Command,\r\n  CommandDialog,\r\n  CommandInput,\r\n  CommandList,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandShortcut,\r\n  CommandSeparator,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA;AACA;AAEA;AACA;AAPA;;;;;;AAeA,SAAS,QAAQ,EACf,SAAS,EACT,GAAG,OAC2C;IAC9C,qBACE,sSAAC,qPAAA,CAAA,UAAgB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,cAAc,EACrB,QAAQ,iBAAiB,EACzB,cAAc,gCAAgC,EAC9C,QAAQ,EACR,GAAG,OAIJ;IACC,qBACE,sSAAC,qIAAA,CAAA,SAAM;QAAE,GAAG,KAAK;;0BACf,sSAAC,qIAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,sSAAC,qIAAA,CAAA,cAAW;kCAAE;;;;;;kCACd,sSAAC,qIAAA,CAAA,oBAAiB;kCAAE;;;;;;;;;;;;0BAEtB,sSAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,sSAAC;oBAAQ,WAAU;8BAChB;;;;;;;;;;;;;;;;;AAKX;MAtBS;AAwBT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,sSAAC;QACC,aAAU;QACV,WAAU;;0BAEV,sSAAC,iSAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BACtB,sSAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4JACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0NACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,SAAS;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;QACrC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,sSAAC,qPAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,sSAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 5920, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/editor/generative/ai-completion-command.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { CommandGroup, CommandItem, CommandSeparator } from \"../../ui/command\";\r\nimport { useEditor } from \"novel\";\r\nimport { Check, TextQuote, TrashIcon } from \"lucide-react\";\r\n\r\nconst AICompletionCommands = ({\r\n  completion,\r\n  onDiscard,\r\n}: {\r\n  completion: string;\r\n  onDiscard: () => void;\r\n}) => {\r\n  const { editor } = useEditor();\r\n  if (!editor) return null;\r\n  return (\r\n    <>\r\n      <CommandGroup>\r\n        <CommandItem\r\n          className=\"gap-2 px-4\"\r\n          value=\"replace\"\r\n          onSelect={() => {\r\n            const selection = editor.view.state.selection;\r\n            editor\r\n              .chain()\r\n              .focus()\r\n              .insertContentAt(\r\n                {\r\n                  from: selection.from,\r\n                  to: selection.to,\r\n                },\r\n                completion,\r\n              )\r\n              .run();\r\n          }}\r\n        >\r\n          <Check className=\"text-muted-foreground h-4 w-4\" />\r\n          Replace selection\r\n        </CommandItem>\r\n        <CommandItem\r\n          className=\"gap-2 px-4\"\r\n          value=\"insert\"\r\n          onSelect={() => {\r\n            const selection = editor.view.state.selection;\r\n            editor\r\n              .chain()\r\n              .focus()\r\n              .insertContentAt(selection.to + 1, completion)\r\n              .run();\r\n          }}\r\n        >\r\n          <TextQuote className=\"text-muted-foreground h-4 w-4\" />\r\n          Insert below\r\n        </CommandItem>\r\n      </CommandGroup>\r\n      <CommandSeparator />\r\n\r\n      <CommandGroup>\r\n        <CommandItem onSelect={onDiscard} value=\"thrash\" className=\"gap-2 px-4\">\r\n          <TrashIcon className=\"text-muted-foreground h-4 w-4\" />\r\n          Discard\r\n        </CommandItem>\r\n      </CommandGroup>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AICompletionCommands;\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AACA;AAAA;AAAA;;;;;;AAEA,MAAM,uBAAuB,CAAC,EAC5B,UAAU,EACV,SAAS,EAIV;;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAC3B,IAAI,CAAC,QAAQ,OAAO;IACpB,qBACE;;0BACE,sSAAC,sIAAA,CAAA,eAAY;;kCACX,sSAAC,sIAAA,CAAA,cAAW;wBACV,WAAU;wBACV,OAAM;wBACN,UAAU;4BACR,MAAM,YAAY,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS;4BAC7C,OACG,KAAK,GACL,KAAK,GACL,eAAe,CACd;gCACE,MAAM,UAAU,IAAI;gCACpB,IAAI,UAAU,EAAE;4BAClB,GACA,YAED,GAAG;wBACR;;0CAEA,sSAAC,2RAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAkC;;;;;;;kCAGrD,sSAAC,sIAAA,CAAA,cAAW;wBACV,WAAU;wBACV,OAAM;wBACN,UAAU;4BACR,MAAM,YAAY,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS;4BAC7C,OACG,KAAK,GACL,KAAK,GACL,eAAe,CAAC,UAAU,EAAE,GAAG,GAAG,YAClC,GAAG;wBACR;;0CAEA,sSAAC,uSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAkC;;;;;;;;;;;;;0BAI3D,sSAAC,sIAAA,CAAA,mBAAgB;;;;;0BAEjB,sSAAC,sIAAA,CAAA,eAAY;0BACX,cAAA,sSAAC,sIAAA,CAAA,cAAW;oBAAC,UAAU;oBAAW,OAAM;oBAAS,WAAU;;sCACzD,sSAAC,+RAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;wBAAkC;;;;;;;;;;;;;;AAMjE;GA3DM;;QAOe,gUAAA,CAAA,YAAS;;;KAPxB;uCA6DS", "debugId": null}}, {"offset": {"line": 6048, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/editor/generative/ai-selector-commands.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport {\r\n  ArrowDownWideNarrow,\r\n  CheckCheck,\r\n  RefreshCcwDot,\r\n  StepForward,\r\n  WrapText,\r\n} from \"lucide-react\";\r\nimport { getPrevText, useEditor } from \"novel\";\r\nimport { CommandGroup, CommandItem, CommandSeparator } from \"../../ui/command\";\r\n\r\nconst options = [\r\n  {\r\n    value: \"improve\",\r\n    label: \"Improve writing\",\r\n    icon: RefreshCcwDot,\r\n  },\r\n  // TODO: add this back in\r\n  // {\r\n  //   value: \"fix\",\r\n  //   label: \"Fix grammar\",\r\n  //   icon: CheckCheck,\r\n  // },\r\n  {\r\n    value: \"shorter\",\r\n    label: \"Make shorter\",\r\n    icon: ArrowDownWideNarrow,\r\n  },\r\n  {\r\n    value: \"longer\",\r\n    label: \"Make longer\",\r\n    icon: WrapText,\r\n  },\r\n];\r\n\r\ninterface AISelectorCommandsProps {\r\n  onSelect: (value: string, option: string) => void;\r\n}\r\n\r\nconst AISelectorCommands = ({ onSelect }: AISelectorCommandsProps) => {\r\n  const { editor } = useEditor();\r\n  if (!editor) return null;\r\n  return (\r\n    <>\r\n      <CommandGroup heading=\"Edit or review selection\">\r\n        {options.map((option) => (\r\n          <CommandItem\r\n            onSelect={(value) => {\r\n              const slice = editor.state.selection.content();\r\n              const text = editor.storage.markdown.serializer.serialize(\r\n                slice.content,\r\n              );\r\n              onSelect(text, value);\r\n            }}\r\n            className=\"flex gap-2 px-4\"\r\n            key={option.value}\r\n            value={option.value}\r\n          >\r\n            <option.icon className=\"h-4 w-4 text-purple-500\" />\r\n            {option.label}\r\n          </CommandItem>\r\n        ))}\r\n      </CommandGroup>\r\n      <CommandSeparator />\r\n      <CommandGroup heading=\"Use AI to do more\">\r\n        <CommandItem\r\n          onSelect={() => {\r\n            const pos = editor.state.selection.from;\r\n            const text = getPrevText(editor, pos);\r\n            onSelect(text, \"continue\");\r\n          }}\r\n          value=\"continue\"\r\n          className=\"gap-2 px-4\"\r\n        >\r\n          <StepForward className=\"h-4 w-4 text-purple-500\" />\r\n          Continue writing\r\n        </CommandItem>\r\n      </CommandGroup>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AISelectorCommands;\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AAAA;AAAA;AAOA;AAAA;AACA;;;;;;AAEA,MAAM,UAAU;IACd;QACE,OAAO;QACP,OAAO;QACP,MAAM,mTAAA,CAAA,gBAAa;IACrB;IACA,yBAAyB;IACzB,IAAI;IACJ,kBAAkB;IAClB,0BAA0B;IAC1B,sBAAsB;IACtB,KAAK;IACL;QACE,OAAO;QACP,OAAO;QACP,MAAM,mUAAA,CAAA,sBAAmB;IAC3B;IACA;QACE,OAAO;QACP,OAAO;QACP,MAAM,qSAAA,CAAA,WAAQ;IAChB;CACD;AAMD,MAAM,qBAAqB,CAAC,EAAE,QAAQ,EAA2B;;IAC/D,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAC3B,IAAI,CAAC,QAAQ,OAAO;IACpB,qBACE;;0BACE,sSAAC,sIAAA,CAAA,eAAY;gBAAC,SAAQ;0BACnB,QAAQ,GAAG,CAAC,CAAC,uBACZ,sSAAC,sIAAA,CAAA,cAAW;wBACV,UAAU,CAAC;4BACT,MAAM,QAAQ,OAAO,KAAK,CAAC,SAAS,CAAC,OAAO;4BAC5C,MAAM,OAAO,OAAO,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CACvD,MAAM,OAAO;4BAEf,SAAS,MAAM;wBACjB;wBACA,WAAU;wBAEV,OAAO,OAAO,KAAK;;0CAEnB,sSAAC,OAAO,IAAI;gCAAC,WAAU;;;;;;4BACtB,OAAO,KAAK;;uBAJR,OAAO,KAAK;;;;;;;;;;0BAQvB,sSAAC,sIAAA,CAAA,mBAAgB;;;;;0BACjB,sSAAC,sIAAA,CAAA,eAAY;gBAAC,SAAQ;0BACpB,cAAA,sSAAC,sIAAA,CAAA,cAAW;oBACV,UAAU;wBACR,MAAM,MAAM,OAAO,KAAK,CAAC,SAAS,CAAC,IAAI;wBACvC,MAAM,OAAO,CAAA,GAAA,kQAAA,CAAA,cAAW,AAAD,EAAE,QAAQ;wBACjC,SAAS,MAAM;oBACjB;oBACA,OAAM;oBACN,WAAU;;sCAEV,sSAAC,2SAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAA4B;;;;;;;;;;;;;;AAM7D;GAzCM;;QACe,gUAAA,CAAA,YAAS;;;KADxB;uCA2CS", "debugId": null}}, {"offset": {"line": 6181, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/editor/generative/ai-selector.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport { Command, CommandInput } from \"../../ui/command\";\r\n\r\nimport { ArrowUp } from \"lucide-react\";\r\nimport { useEditor } from \"novel\";\r\nimport { addA<PERSON><PERSON>ighlight } from \"novel\";\r\nimport { useCallback, useState } from \"react\";\r\nimport Markdown from \"react-markdown\";\r\nimport { toast } from \"sonner\";\r\nimport { Button } from \"../../ui/button\";\r\nimport Magic from \"../../ui/icons/magic\";\r\nimport { ScrollArea } from \"../../ui/scroll-area\";\r\nimport AICompletionCommands from \"./ai-completion-command\";\r\nimport AISelectorCommands from \"./ai-selector-commands\";\r\nimport { LoadingOutlined } from \"@ant-design/icons\";\r\nimport { resolveServiceURL } from \"~/core/api/resolve-service-url\";\r\nimport { fetchStream } from \"~/core/sse\";\r\n//TODO: I think it makes more sense to create a custom Tiptap extension for this functionality https://tiptap.dev/docs/editor/ai/introduction\r\n\r\ninterface AISelectorProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n}\r\n\r\nfunction useProseCompletion() {\r\n  const [completion, setCompletion] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<Error | null>(null);\r\n\r\n  const complete = useCallback(\r\n    async (prompt: string, options?: { body?: Record<string, any> }) => {\r\n      setIsLoading(true);\r\n      setError(null);\r\n\r\n      try {\r\n        const response = await fetchStream(\r\n          resolveServiceURL(\"/api/prose/generate\"),\r\n          {\r\n            method: \"POST\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n            body: JSON.stringify({\r\n              prompt,\r\n              ...options?.body,\r\n            }),\r\n          },\r\n        );\r\n\r\n        let fullText = \"\";\r\n\r\n        // Process the streaming response\r\n        for await (const chunk of response) {\r\n          fullText += chunk.data;\r\n          setCompletion(fullText);\r\n        }\r\n\r\n        setIsLoading(false);\r\n        return fullText;\r\n      } catch (e) {\r\n        const error = e instanceof Error ? e : new Error(\"An error occurred\");\r\n        setError(error);\r\n        toast.error(error.message);\r\n        setIsLoading(false);\r\n        throw error;\r\n      }\r\n    },\r\n    [],\r\n  );\r\n\r\n  const reset = useCallback(() => {\r\n    setCompletion(\"\");\r\n    setError(null);\r\n    setIsLoading(false);\r\n  }, []);\r\n\r\n  return {\r\n    completion,\r\n    complete,\r\n    isLoading,\r\n    error,\r\n    reset,\r\n  };\r\n}\r\n\r\nexport function AISelector({ onOpenChange }: AISelectorProps) {\r\n  const { editor } = useEditor();\r\n  const [inputValue, setInputValue] = useState(\"\");\r\n\r\n  const { completion, complete, isLoading } = useProseCompletion();\r\n\r\n  if (!editor) return null;\r\n\r\n  const hasCompletion = completion.length > 0;\r\n\r\n  return (\r\n    <Command className=\"w-[350px]\">\r\n      {hasCompletion && (\r\n        <div className=\"flex max-h-[400px]\">\r\n          <ScrollArea>\r\n            <div className=\"prose prose-sm dark:prose-invert p-2 px-4\">\r\n              <Markdown>{completion}</Markdown>\r\n            </div>\r\n          </ScrollArea>\r\n        </div>\r\n      )}\r\n\r\n      {isLoading && (\r\n        <div className=\"flex h-12 w-full items-center px-4 text-sm font-medium text-purple-500\">\r\n          <Magic className=\"mr-2 h-4 w-4 shrink-0\" />\r\n          AI is thinking\r\n          <div className=\"mt-1 ml-2\">\r\n            <LoadingOutlined />\r\n          </div>\r\n        </div>\r\n      )}\r\n      {!isLoading && (\r\n        <>\r\n          <div className=\"relative\">\r\n            <CommandInput\r\n              value={inputValue}\r\n              onValueChange={setInputValue}\r\n              autoFocus\r\n              placeholder={\r\n                hasCompletion\r\n                  ? \"Tell AI what to do next\"\r\n                  : \"Ask AI to edit or generate...\"\r\n              }\r\n              onFocus={() => addAIHighlight(editor)}\r\n            />\r\n            <Button\r\n              size=\"icon\"\r\n              className=\"absolute top-1/2 right-2 h-6 w-6 -translate-y-1/2 rounded-full bg-purple-500 hover:bg-purple-900\"\r\n              onClick={() => {\r\n                if (completion)\r\n                  return complete(completion, {\r\n                    body: { option: \"zap\", command: inputValue },\r\n                  }).then(() => setInputValue(\"\"));\r\n\r\n                const slice = editor.state.selection.content();\r\n                const text = editor.storage.markdown.serializer.serialize(\r\n                  slice.content,\r\n                );\r\n\r\n                complete(text, {\r\n                  body: { option: \"zap\", command: inputValue },\r\n                }).then(() => setInputValue(\"\"));\r\n              }}\r\n            >\r\n              <ArrowUp className=\"h-4 w-4\" />\r\n            </Button>\r\n          </div>\r\n          {hasCompletion ? (\r\n            <AICompletionCommands\r\n              onDiscard={() => {\r\n                editor.chain().unsetHighlight().focus().run();\r\n                onOpenChange(false);\r\n              }}\r\n              completion={completion}\r\n            />\r\n          ) : (\r\n            <AISelectorCommands\r\n              onSelect={(value, option) =>\r\n                complete(value, { body: { option } })\r\n              }\r\n            />\r\n          )}\r\n        </>\r\n      )}\r\n    </Command>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAjBA;;;;;;;;;;;;;;;;AAyBA,SAAS;;IACP,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAgB;IAEjD,MAAM,WAAW,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;oDACzB,OAAO,QAAgB;YACrB,aAAa;YACb,SAAS;YAET,IAAI;gBACF,MAAM,WAAW,MAAM,CAAA,GAAA,wIAAA,CAAA,cAAW,AAAD,EAC/B,CAAA,GAAA,kJAAA,CAAA,oBAAiB,AAAD,EAAE,wBAClB;oBACE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA,GAAG,SAAS,IAAI;oBAClB;gBACF;gBAGF,IAAI,WAAW;gBAEf,iCAAiC;gBACjC,WAAW,MAAM,SAAS,SAAU;oBAClC,YAAY,MAAM,IAAI;oBACtB,cAAc;gBAChB;gBAEA,aAAa;gBACb,OAAO;YACT,EAAE,OAAO,GAAG;gBACV,MAAM,QAAQ,aAAa,QAAQ,IAAI,IAAI,MAAM;gBACjD,SAAS;gBACT,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO;gBACzB,aAAa;gBACb,MAAM;YACR;QACF;mDACA,EAAE;IAGJ,MAAM,QAAQ,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;iDAAE;YACxB,cAAc;YACd,SAAS;YACT,aAAa;QACf;gDAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;GA3DS;AA6DF,SAAS,WAAW,EAAE,YAAY,EAAmB;;IAC1D,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG;IAE5C,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,gBAAgB,WAAW,MAAM,GAAG;IAE1C,qBACE,sSAAC,sIAAA,CAAA,UAAO;QAAC,WAAU;;YAChB,+BACC,sSAAC;gBAAI,WAAU;0BACb,cAAA,sSAAC,6IAAA,CAAA,aAAU;8BACT,cAAA,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC,mTAAA,CAAA,UAAQ;sCAAE;;;;;;;;;;;;;;;;;;;;;YAMlB,2BACC,sSAAC;gBAAI,WAAU;;kCACb,sSAAC,6IAAA,CAAA,UAAK;wBAAC,WAAU;;;;;;oBAA0B;kCAE3C,sSAAC;wBAAI,WAAU;kCACb,cAAA,sSAAC,qUAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;YAIrB,CAAC,2BACA;;kCACE,sSAAC;wBAAI,WAAU;;0CACb,sSAAC,sIAAA,CAAA,eAAY;gCACX,OAAO;gCACP,eAAe;gCACf,SAAS;gCACT,aACE,gBACI,4BACA;gCAEN,SAAS,IAAM,CAAA,GAAA,kQAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;0CAEhC,sSAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,WAAU;gCACV,SAAS;oCACP,IAAI,YACF,OAAO,SAAS,YAAY;wCAC1B,MAAM;4CAAE,QAAQ;4CAAO,SAAS;wCAAW;oCAC7C,GAAG,IAAI,CAAC,IAAM,cAAc;oCAE9B,MAAM,QAAQ,OAAO,KAAK,CAAC,SAAS,CAAC,OAAO;oCAC5C,MAAM,OAAO,OAAO,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,SAAS,CACvD,MAAM,OAAO;oCAGf,SAAS,MAAM;wCACb,MAAM;4CAAE,QAAQ;4CAAO,SAAS;wCAAW;oCAC7C,GAAG,IAAI,CAAC,IAAM,cAAc;gCAC9B;0CAEA,cAAA,sSAAC,mSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAGtB,8BACC,sSAAC,4KAAA,CAAA,UAAoB;wBACnB,WAAW;4BACT,OAAO,KAAK,GAAG,cAAc,GAAG,KAAK,GAAG,GAAG;4BAC3C,aAAa;wBACf;wBACA,YAAY;;;;;6CAGd,sSAAC,2KAAA,CAAA,UAAkB;wBACjB,UAAU,CAAC,OAAO,SAChB,SAAS,OAAO;gCAAE,MAAM;oCAAE;gCAAO;4BAAE;;;;;;;;;;;;;;AAQnD;IAtFgB;;QACK,gUAAA,CAAA,YAAS;QAGgB;;;KAJ9B", "debugId": null}}, {"offset": {"line": 6441, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/editor/generative/generative-menu-switch.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { EditorBubble, removeAIHighlight, useEditor } from \"novel\";\r\nimport { Fragment, type ReactNode, useEffect } from \"react\";\r\nimport { Button } from \"../../ui/button\";\r\nimport Magic from \"../../ui/icons/magic\";\r\nimport { AISelector } from \"./ai-selector\";\r\nimport { useReplay } from \"~/core/replay\";\r\nimport { TooltipContent, TooltipTrigger } from \"~/components/ui/tooltip\";\r\nimport { Tooltip } from \"~/components/ui/tooltip\";\r\n\r\ninterface GenerativeMenuSwitchProps {\r\n  children: ReactNode;\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n}\r\nconst GenerativeMenuSwitch = ({\r\n  children,\r\n  open,\r\n  onOpenChange,\r\n}: GenerativeMenuSwitchProps) => {\r\n  const { editor } = useEditor();\r\n  const { isReplay } = useReplay();\r\n  useEffect(() => {\r\n    if (!open && editor) removeAIHighlight(editor);\r\n  }, [open]);\r\n\r\n  if (!editor) return null;\r\n  return (\r\n    <EditorBubble\r\n      tippyOptions={{\r\n        placement: open ? \"bottom-start\" : \"top\",\r\n        onHidden: () => {\r\n          onOpenChange(false);\r\n          editor.chain().unsetHighlight().run();\r\n        },\r\n      }}\r\n      className=\"border-muted bg-background flex w-fit max-w-[90vw] overflow-hidden rounded-md border shadow-xl\"\r\n    >\r\n      {open && <AISelector open={open} onOpenChange={onOpenChange} />}\r\n      {!open && (\r\n        <Fragment>\r\n          {isReplay ? (\r\n            <Tooltip>\r\n              <TooltipTrigger>\r\n                <Button\r\n                  className=\"gap-1 rounded-none text-purple-500\"\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  disabled\r\n                >\r\n                  <Magic className=\"h-5 w-5\" />\r\n                  Ask AI\r\n                </Button>\r\n              </TooltipTrigger>\r\n              <TooltipContent>You can't ask AI in replay mode.</TooltipContent>\r\n            </Tooltip>\r\n          ) : (\r\n            <Button\r\n              className=\"gap-1 rounded-none text-purple-500\"\r\n              variant=\"ghost\"\r\n              onClick={() => onOpenChange(true)}\r\n              size=\"sm\"\r\n            >\r\n              <Magic className=\"h-5 w-5\" />\r\n              Ask AI\r\n            </Button>\r\n          )}\r\n          {children}\r\n        </Fragment>\r\n      )}\r\n    </EditorBubble>\r\n  );\r\n};\r\n\r\nexport default GenerativeMenuSwitch;\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;;;AAQA,MAAM,uBAAuB,CAAC,EAC5B,QAAQ,EACR,IAAI,EACJ,YAAY,EACc;;IAC1B,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IAC7B,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,CAAC,QAAQ,QAAQ,CAAA,GAAA,kQAAA,CAAA,oBAAiB,AAAD,EAAE;QACzC;yCAAG;QAAC;KAAK;IAET,IAAI,CAAC,QAAQ,OAAO;IACpB,qBACE,sSAAC,kQAAA,CAAA,eAAY;QACX,cAAc;YACZ,WAAW,OAAO,iBAAiB;YACnC,UAAU;gBACR,aAAa;gBACb,OAAO,KAAK,GAAG,cAAc,GAAG,GAAG;YACrC;QACF;QACA,WAAU;;YAET,sBAAQ,sSAAC,+JAAA,CAAA,aAAU;gBAAC,MAAM;gBAAM,cAAc;;;;;;YAC9C,CAAC,sBACA,sSAAC,sQAAA,CAAA,WAAQ;;oBACN,yBACC,sSAAC,sIAAA,CAAA,UAAO;;0CACN,sSAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAQ;oCACR,MAAK;oCACL,QAAQ;;sDAER,sSAAC,6IAAA,CAAA,UAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIjC,sSAAC,sIAAA,CAAA,iBAAc;0CAAC;;;;;;;;;;;6CAGlB,sSAAC,qIAAA,CAAA,SAAM;wBACL,WAAU;wBACV,SAAQ;wBACR,SAAS,IAAM,aAAa;wBAC5B,MAAK;;0CAEL,sSAAC,6IAAA,CAAA,UAAK;gCAAC,WAAU;;;;;;4BAAY;;;;;;;oBAIhC;;;;;;;;;;;;;AAKX;GAzDM;;QAKe,gUAAA,CAAA,YAAS;QACP,iIAAA,CAAA,YAAS;;;KAN1B;uCA2DS", "debugId": null}}, {"offset": {"line": 6591, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/editor/image-upload.ts"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { createImageUpload } from \"novel\";\r\nimport { toast } from \"sonner\";\r\n\r\nconst onUpload = (file: File) => {\r\n  const promise = fetch(\"/api/upload\", {\r\n    method: \"POST\",\r\n    headers: {\r\n      \"content-type\": file?.type || \"application/octet-stream\",\r\n      \"x-vercel-filename\": file?.name || \"image.png\",\r\n    },\r\n    body: file,\r\n  });\r\n\r\n  return new Promise((resolve, reject) => {\r\n    toast.promise(\r\n      promise.then(async (res) => {\r\n        // Successfully uploaded image\r\n        if (res.status === 200) {\r\n          const { url } = (await res.json()) as { url: string };\r\n          // preload the image\r\n          const image = new Image();\r\n          image.src = url;\r\n          image.onload = () => {\r\n            resolve(url);\r\n          };\r\n          // No blob store configured\r\n        } else if (res.status === 401) {\r\n          resolve(file);\r\n          throw new Error(\r\n            \"`BLOB_READ_WRITE_TOKEN` environment variable not found, reading image locally instead.\",\r\n          );\r\n          // Unknown error\r\n        } else {\r\n          throw new Error(\"Error uploading image. Please try again.\");\r\n        }\r\n      }),\r\n      {\r\n        loading: \"Uploading image...\",\r\n        success: \"Image uploaded successfully.\",\r\n        error: (e) => {\r\n          reject(e);\r\n          return e.message;\r\n        },\r\n      },\r\n    );\r\n  });\r\n};\r\n\r\nexport const uploadFn = createImageUpload({\r\n  onUpload,\r\n  validateFn: (file) => {\r\n    if (!file.type.includes(\"image/\")) {\r\n      toast.error(\"File type not supported.\");\r\n      return false;\r\n    }\r\n    if (file.size / 1024 / 1024 > 20) {\r\n      toast.error(\"File size too big (max 20MB).\");\r\n      return false;\r\n    }\r\n    return true;\r\n  },\r\n});\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;AAE/B;AACA;;;AAEA,MAAM,WAAW,CAAC;IAChB,MAAM,UAAU,MAAM,eAAe;QACnC,QAAQ;QACR,SAAS;YACP,gBAAgB,MAAM,QAAQ;YAC9B,qBAAqB,MAAM,QAAQ;QACrC;QACA,MAAM;IACR;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,2QAAA,CAAA,QAAK,CAAC,OAAO,CACX,QAAQ,IAAI,CAAC,OAAO;YAClB,8BAA8B;YAC9B,IAAI,IAAI,MAAM,KAAK,KAAK;gBACtB,MAAM,EAAE,GAAG,EAAE,GAAI,MAAM,IAAI,IAAI;gBAC/B,oBAAoB;gBACpB,MAAM,QAAQ,IAAI;gBAClB,MAAM,GAAG,GAAG;gBACZ,MAAM,MAAM,GAAG;oBACb,QAAQ;gBACV;YACA,2BAA2B;YAC7B,OAAO,IAAI,IAAI,MAAM,KAAK,KAAK;gBAC7B,QAAQ;gBACR,MAAM,IAAI,MACR;YAEF,gBAAgB;YAClB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,IACA;YACE,SAAS;YACT,SAAS;YACT,OAAO,CAAC;gBACN,OAAO;gBACP,OAAO,EAAE,OAAO;YAClB;QACF;IAEJ;AACF;AAEO,MAAM,WAAW,CAAA,GAAA,kQAAA,CAAA,oBAAiB,AAAD,EAAE;IACxC;IACA,YAAY,CAAC;QACX,IAAI,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,WAAW;YACjC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;QACA,IAAI,KAAK,IAAI,GAAG,OAAO,OAAO,IAAI;YAChC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;QACA,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 6661, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/editor/selectors/text-buttons.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Button } from \"../../ui/button\";\r\nimport { cn } from \"../../../lib/utils\";\r\nimport {\r\n  BoldIcon,\r\n  CodeIcon,\r\n  ItalicIcon,\r\n  StrikethroughIcon,\r\n  UnderlineIcon,\r\n} from \"lucide-react\";\r\nimport { EditorBubbleItem, useEditor } from \"novel\";\r\nimport type { SelectorItem } from \"./node-selector\";\r\n\r\nexport const TextButtons = () => {\r\n  const { editor } = useEditor();\r\n  if (!editor) return null;\r\n  const items: SelectorItem[] = [\r\n    {\r\n      name: \"bold\",\r\n      isActive: (editor) => editor.isActive(\"bold\"),\r\n      command: (editor) => editor.chain().focus().toggleBold().run(),\r\n      icon: BoldIcon,\r\n    },\r\n    {\r\n      name: \"italic\",\r\n      isActive: (editor) => editor.isActive(\"italic\"),\r\n      command: (editor) => editor.chain().focus().toggleItalic().run(),\r\n      icon: ItalicIcon,\r\n    },\r\n    {\r\n      name: \"underline\",\r\n      isActive: (editor) => editor.isActive(\"underline\"),\r\n      command: (editor) => editor.chain().focus().toggleUnderline().run(),\r\n      icon: UnderlineIcon,\r\n    },\r\n    {\r\n      name: \"strike\",\r\n      isActive: (editor) => editor.isActive(\"strike\"),\r\n      command: (editor) => editor.chain().focus().toggleStrike().run(),\r\n      icon: StrikethroughIcon,\r\n    },\r\n    {\r\n      name: \"code\",\r\n      isActive: (editor) => editor.isActive(\"code\"),\r\n      command: (editor) => editor.chain().focus().toggleCode().run(),\r\n      icon: CodeIcon,\r\n    },\r\n  ];\r\n  return (\r\n    <div className=\"flex\">\r\n      {items.map((item) => (\r\n        <EditorBubbleItem\r\n          key={item.name}\r\n          onSelect={(editor) => {\r\n            item.command(editor);\r\n          }}\r\n        >\r\n          <Button\r\n            size=\"sm\"\r\n            className=\"rounded-none\"\r\n            variant=\"ghost\"\r\n            type=\"button\"\r\n          >\r\n            <item.icon\r\n              className={cn(\"h-4 w-4\", {\r\n                \"text-blue-500\": item.isActive(editor),\r\n              })}\r\n            />\r\n          </Button>\r\n        </EditorBubbleItem>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAAA;;;;;;;AAGO,MAAM,cAAc;;IACzB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,gUAAA,CAAA,YAAS,AAAD;IAC3B,IAAI,CAAC,QAAQ,OAAO;IACpB,MAAM,QAAwB;QAC5B;YACE,MAAM;YACN,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;YACtC,SAAS,CAAC,SAAW,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;YAC5D,MAAM,6RAAA,CAAA,WAAQ;QAChB;QACA;YACE,MAAM;YACN,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;YACtC,SAAS,CAAC,SAAW,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;YAC9D,MAAM,iSAAA,CAAA,aAAU;QAClB;QACA;YACE,MAAM;YACN,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;YACtC,SAAS,CAAC,SAAW,OAAO,KAAK,GAAG,KAAK,GAAG,eAAe,GAAG,GAAG;YACjE,MAAM,uSAAA,CAAA,gBAAa;QACrB;QACA;YACE,MAAM;YACN,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;YACtC,SAAS,CAAC,SAAW,OAAO,KAAK,GAAG,KAAK,GAAG,YAAY,GAAG,GAAG;YAC9D,MAAM,+SAAA,CAAA,oBAAiB;QACzB;QACA;YACE,MAAM;YACN,UAAU,CAAC,SAAW,OAAO,QAAQ,CAAC;YACtC,SAAS,CAAC,SAAW,OAAO,KAAK,GAAG,KAAK,GAAG,UAAU,GAAG,GAAG;YAC5D,MAAM,6RAAA,CAAA,WAAQ;QAChB;KACD;IACD,qBACE,sSAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,qBACV,sSAAC,kQAAA,CAAA,mBAAgB;gBAEf,UAAU,CAAC;oBACT,KAAK,OAAO,CAAC;gBACf;0BAEA,cAAA,sSAAC,qIAAA,CAAA,SAAM;oBACL,MAAK;oBACL,WAAU;oBACV,SAAQ;oBACR,MAAK;8BAEL,cAAA,sSAAC,KAAK,IAAI;wBACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;4BACvB,iBAAiB,KAAK,QAAQ,CAAC;wBACjC;;;;;;;;;;;eAdC,KAAK,IAAI;;;;;;;;;;AAqBxB;GA5Da;;QACQ,gUAAA,CAAA,YAAS;;;KADjB", "debugId": null}}, {"offset": {"line": 6771, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/editor/slash-command.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport {\r\n  CheckSquare,\r\n  Code,\r\n  Heading1,\r\n  Heading2,\r\n  Heading3,\r\n  List,\r\n  ListOrdered,\r\n  Text,\r\n  TextQuote,\r\n} from \"lucide-react\";\r\nimport { Command, createSuggestionItems, renderItems } from \"novel\";\r\n// import { uploadFn } from \"./image-upload\";\r\n\r\nexport const suggestionItems = createSuggestionItems([\r\n  {\r\n    title: \"Text\",\r\n    description: \"Just start typing with plain text.\",\r\n    searchTerms: [\"p\", \"paragraph\"],\r\n    icon: <Text size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .toggleNode(\"paragraph\", \"paragraph\")\r\n        .run();\r\n    },\r\n  },\r\n  {\r\n    title: \"To-do List\",\r\n    description: \"Track tasks with a to-do list.\",\r\n    searchTerms: [\"todo\", \"task\", \"list\", \"check\", \"checkbox\"],\r\n    icon: <CheckSquare size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor.chain().focus().deleteRange(range).toggleTaskList().run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Heading 1\",\r\n    description: \"Big section heading.\",\r\n    searchTerms: [\"title\", \"big\", \"large\"],\r\n    icon: <Heading1 size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .setNode(\"heading\", { level: 1 })\r\n        .run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Heading 2\",\r\n    description: \"Medium section heading.\",\r\n    searchTerms: [\"subtitle\", \"medium\"],\r\n    icon: <Heading2 size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .setNode(\"heading\", { level: 2 })\r\n        .run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Heading 3\",\r\n    description: \"Small section heading.\",\r\n    searchTerms: [\"subtitle\", \"small\"],\r\n    icon: <Heading3 size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .setNode(\"heading\", { level: 3 })\r\n        .run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Bullet List\",\r\n    description: \"Create a simple bullet list.\",\r\n    searchTerms: [\"unordered\", \"point\"],\r\n    icon: <List size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor.chain().focus().deleteRange(range).toggleBulletList().run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Numbered List\",\r\n    description: \"Create a list with numbering.\",\r\n    searchTerms: [\"ordered\"],\r\n    icon: <ListOrdered size={18} />,\r\n    command: ({ editor, range }) => {\r\n      editor.chain().focus().deleteRange(range).toggleOrderedList().run();\r\n    },\r\n  },\r\n  {\r\n    title: \"Quote\",\r\n    description: \"Capture a quote.\",\r\n    searchTerms: [\"blockquote\"],\r\n    icon: <TextQuote size={18} />,\r\n    command: ({ editor, range }) =>\r\n      editor\r\n        .chain()\r\n        .focus()\r\n        .deleteRange(range)\r\n        .toggleNode(\"paragraph\", \"paragraph\")\r\n        .toggleBlockquote()\r\n        .run(),\r\n  },\r\n  {\r\n    title: \"Code\",\r\n    description: \"Capture a code snippet.\",\r\n    searchTerms: [\"codeblock\"],\r\n    icon: <Code size={18} />,\r\n    command: ({ editor, range }) =>\r\n      editor.chain().focus().deleteRange(range).toggleCodeBlock().run(),\r\n  },\r\n  // {\r\n  //   title: \"Image\",\r\n  //   description: \"Upload an image from your computer.\",\r\n  //   searchTerms: [\"photo\", \"picture\", \"media\"],\r\n  //   icon: <ImageIcon size={18} />,\r\n  //   command: ({ editor, range }) => {\r\n  //     editor.chain().focus().deleteRange(range).run();\r\n  //     // upload image\r\n  //     const input = document.createElement(\"input\");\r\n  //     input.type = \"file\";\r\n  //     input.accept = \"image/*\";\r\n  //     input.onchange = async () => {\r\n  //       if (input.files?.length) {\r\n  //         const file = input.files[0];\r\n  //         if (!file) return;\r\n  //         const pos = editor.view.state.selection.from;\r\n  //         uploadFn(file, editor.view, pos);\r\n  //       }\r\n  //     };\r\n  //     input.click();\r\n  //   },\r\n  // },\r\n  // {\r\n  //   title: \"Youtube\",\r\n  //   description: \"Embed a Youtube video.\",\r\n  //   searchTerms: [\"video\", \"youtube\", \"embed\"],\r\n  //   icon: <Youtube size={18} />,\r\n  //   command: ({ editor, range }) => {\r\n  //     const videoLink = prompt(\"Please enter Youtube Video Link\");\r\n  //     //From https://regexr.com/3dj5t\r\n  //     const ytRegex = new RegExp(\r\n  //       /^((?:https?:)?\\/\\/)?((?:www|m)\\.)?((?:youtube\\.com|youtu.be))(\\/(?:[\\w\\-]+\\?v=|embed\\/|v\\/)?)([\\w\\-]+)(\\S+)?$/,\r\n  //     );\r\n\r\n  //     if (videoLink && ytRegex.test(videoLink)) {\r\n  //       editor\r\n  //         .chain()\r\n  //         .focus()\r\n  //         .deleteRange(range)\r\n  //         .setYoutubeVideo({\r\n  //           src: videoLink,\r\n  //         })\r\n  //         .run();\r\n  //     } else {\r\n  //       if (videoLink !== null) {\r\n  //         alert(\"Please enter a correct Youtube Video Link\");\r\n  //       }\r\n  //     }\r\n  //   },\r\n  // },\r\n  // {\r\n  //   title: \"Twitter\",\r\n  //   description: \"Embed a Tweet.\",\r\n  //   searchTerms: [\"twitter\", \"embed\"],\r\n  //   icon: <Twitter size={18} />,\r\n  //   command: ({ editor, range }) => {\r\n  //     const tweetLink = prompt(\"Please enter Twitter Link\");\r\n  //     const tweetRegex = new RegExp(\r\n  //       /^https?:\\/\\/(www\\.)?x\\.com\\/([a-zA-Z0-9_]{1,15})(\\/status\\/(\\d+))?(\\/\\S*)?$/,\r\n  //     );\r\n\r\n  //     if (tweetLink && tweetRegex.test(tweetLink)) {\r\n  //       editor\r\n  //         .chain()\r\n  //         .focus()\r\n  //         .deleteRange(range)\r\n  //         .setTweet({\r\n  //           src: tweetLink,\r\n  //         })\r\n  //         .run();\r\n  //     } else {\r\n  //       if (tweetLink !== null) {\r\n  //         alert(\"Please enter a correct Twitter Link\");\r\n  //       }\r\n  //     }\r\n  //   },\r\n  // },\r\n]);\r\n\r\nexport const slashCommand = Command.configure({\r\n  suggestion: {\r\n    items: () => suggestionItems,\r\n    render: renderItems,\r\n  },\r\n});\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;;AAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;;AAGO,MAAM,kBAAkB,CAAA,GAAA,kQAAA,CAAA,wBAAqB,AAAD,EAAE;IACnD;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAK;SAAY;QAC/B,oBAAM,sSAAC,yRAAA,CAAA,OAAI;YAAC,MAAM;;;;;;QAClB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,UAAU,CAAC,aAAa,aACxB,GAAG;QACR;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAQ;YAAQ;YAAQ;YAAS;SAAW;QAC1D,oBAAM,sSAAC,kTAAA,CAAA,cAAW;YAAC,MAAM;;;;;;QACzB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,OAAO,cAAc,GAAG,GAAG;QAChE;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAS;YAAO;SAAQ;QACtC,oBAAM,sSAAC,qSAAA,CAAA,WAAQ;YAAC,MAAM;;;;;;QACtB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,OAAO,CAAC,WAAW;gBAAE,OAAO;YAAE,GAC9B,GAAG;QACR;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAY;SAAS;QACnC,oBAAM,sSAAC,qSAAA,CAAA,WAAQ;YAAC,MAAM;;;;;;QACtB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,OAAO,CAAC,WAAW;gBAAE,OAAO;YAAE,GAC9B,GAAG;QACR;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAY;SAAQ;QAClC,oBAAM,sSAAC,qSAAA,CAAA,WAAQ;YAAC,MAAM;;;;;;QACtB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,OAAO,CAAC,WAAW;gBAAE,OAAO;YAAE,GAC9B,GAAG;QACR;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;YAAa;SAAQ;QACnC,oBAAM,sSAAC,yRAAA,CAAA,OAAI;YAAC,MAAM;;;;;;QAClB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,OAAO,gBAAgB,GAAG,GAAG;QAClE;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;SAAU;QACxB,oBAAM,sSAAC,2SAAA,CAAA,cAAW;YAAC,MAAM;;;;;;QACzB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE;YACzB,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,OAAO,iBAAiB,GAAG,GAAG;QACnE;IACF;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;SAAa;QAC3B,oBAAM,sSAAC,uSAAA,CAAA,YAAS;YAAC,MAAM;;;;;;QACvB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GACzB,OACG,KAAK,GACL,KAAK,GACL,WAAW,CAAC,OACZ,UAAU,CAAC,aAAa,aACxB,gBAAgB,GAChB,GAAG;IACV;IACA;QACE,OAAO;QACP,aAAa;QACb,aAAa;YAAC;SAAY;QAC1B,oBAAM,sSAAC,yRAAA,CAAA,OAAI;YAAC,MAAM;;;;;;QAClB,SAAS,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,GACzB,OAAO,KAAK,GAAG,KAAK,GAAG,WAAW,CAAC,OAAO,eAAe,GAAG,GAAG;IACnE;CA8ED;AAEM,MAAM,eAAe,kQAAA,CAAA,UAAO,CAAC,SAAS,CAAC;IAC5C,YAAY;QACV,OAAO,IAAM;QACb,QAAQ,kQAAA,CAAA,cAAW;IACrB;AACF", "debugId": null}}, {"offset": {"line": 6973, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/components/editor/index.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport {\r\n  Editor<PERSON><PERSON><PERSON>,\r\n  EditorCommandEmpty,\r\n  Editor<PERSON><PERSON>mand<PERSON><PERSON>,\r\n  Editor<PERSON><PERSON><PERSON>List,\r\n  EditorContent,\r\n  type EditorInstance,\r\n  EditorRoot,\r\n  ImageResizer,\r\n  type JSONContent,\r\n  handleCommandNavigation,\r\n  handleImageDrop,\r\n  handleImagePaste,\r\n} from \"novel\";\r\nimport type { Content } from \"@tiptap/react\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { useDebouncedCallback } from \"use-debounce\";\r\nimport { defaultExtensions } from \"./extensions\";\r\nimport { ColorSelector } from \"./selectors/color-selector\";\r\nimport { LinkSelector } from \"./selectors/link-selector\";\r\nimport { MathSelector } from \"./selectors/math-selector\";\r\nimport { NodeSelector } from \"./selectors/node-selector\";\r\nimport { Separator } from \"../ui/separator\";\r\n\r\nimport GenerativeMenuSwitch from \"./generative/generative-menu-switch\";\r\nimport { uploadFn } from \"./image-upload\";\r\nimport { TextButtons } from \"./selectors/text-buttons\";\r\nimport { slashCommand, suggestionItems } from \"./slash-command\";\r\n// import { defaultEditorContent } from \"./content\";\r\n\r\nimport \"~/styles/prosemirror.css\";\r\n\r\nconst hljs = require(\"highlight.js\");\r\n\r\nconst extensions = [...defaultExtensions, slashCommand];\r\n\r\nexport interface ReportEditorProps {\r\n  content: Content;\r\n  onMarkdownChange?: (markdown: string) => void;\r\n}\r\n\r\nconst ReportEditor = ({ content, onMarkdownChange }: ReportEditorProps) => {\r\n  const [initialContent, setInitialContent] = useState<Content>(() => content);\r\n  const [saveStatus, setSaveStatus] = useState(\"Saved\");\r\n\r\n  const [openNode, setOpenNode] = useState(false);\r\n  const [openColor, setOpenColor] = useState(false);\r\n  const [openLink, setOpenLink] = useState(false);\r\n  const [openAI, setOpenAI] = useState(false);\r\n\r\n  //Apply Codeblock Highlighting on the HTML from editor.getHTML()\r\n  const highlightCodeblocks = (content: string) => {\r\n    const doc = new DOMParser().parseFromString(content, \"text/html\");\r\n    doc.querySelectorAll(\"pre code\").forEach((el) => {\r\n      // @ts-ignore\r\n      // https://highlightjs.readthedocs.io/en/latest/api.html?highlight=highlightElement#highlightelement\r\n      hljs.highlightElement(el);\r\n    });\r\n    return new XMLSerializer().serializeToString(doc);\r\n  };\r\n\r\n  const debouncedUpdates = useDebouncedCallback(\r\n    async (editor: EditorInstance) => {\r\n      // const json = editor.getJSON();\r\n      // // setCharsCount(editor.storage.characterCount.words());\r\n      // window.localStorage.setItem(\r\n      //   \"html-content\",\r\n      //   highlightCodeblocks(editor.getHTML()),\r\n      // );\r\n      // window.localStorage.setItem(\"novel-content\", JSON.stringify(json));\r\n      // window.localStorage.setItem(\r\n      //   \"markdown\",\r\n      //   editor.storage.markdown.getMarkdown(),\r\n      // );\r\n      if (onMarkdownChange) {\r\n        const markdown = editor.storage.markdown.getMarkdown();\r\n        onMarkdownChange(markdown);\r\n      }\r\n      setSaveStatus(\"Saved\");\r\n    },\r\n    500,\r\n  );\r\n\r\n  // useEffect(() => {\r\n  //   const content = window.localStorage.getItem(\"novel-content\");\r\n  //   if (content) setInitialContent(JSON.parse(content));\r\n  //   else setInitialContent(defaultEditorContent);\r\n  // }, []);\r\n\r\n  if (!initialContent) return null;\r\n\r\n  return (\r\n    <div className=\"relative w-full\">\r\n      <EditorRoot>\r\n        <EditorContent\r\n          immediatelyRender={false}\r\n          initialContent={initialContent as JSONContent}\r\n          extensions={extensions}\r\n          className=\"border-muted relative h-full w-full\"\r\n          editorProps={{\r\n            handleDOMEvents: {\r\n              keydown: (_view, event) => handleCommandNavigation(event),\r\n            },\r\n            handlePaste: (view, event) =>\r\n              handleImagePaste(view, event, uploadFn),\r\n            handleDrop: (view, event, _slice, moved) =>\r\n              handleImageDrop(view, event, moved, uploadFn),\r\n            attributes: {\r\n              class:\r\n                \"prose prose-base prose-p:my-4 dark:prose-invert prose-headings:font-title font-default focus:outline-none max-w-full\",\r\n            },\r\n          }}\r\n          onUpdate={({ editor }) => {\r\n            debouncedUpdates(editor);\r\n            setSaveStatus(\"Unsaved\");\r\n          }}\r\n          slotAfter={<ImageResizer />}\r\n        >\r\n          <EditorCommand className=\"border-muted bg-background z-50 h-auto max-h-[330px] overflow-y-auto rounded-md border px-1 py-2 shadow-md transition-all\">\r\n            <EditorCommandEmpty className=\"text-muted-foreground px-2\">\r\n              No results\r\n            </EditorCommandEmpty>\r\n            <EditorCommandList>\r\n              {suggestionItems.map((item) => (\r\n                <EditorCommandItem\r\n                  value={item.title}\r\n                  onCommand={(val) => item.command?.(val)}\r\n                  className=\"hover:bg-accent aria-selected:bg-accent flex w-full items-center space-x-2 rounded-md px-2 py-1 text-left text-sm\"\r\n                  key={item.title}\r\n                >\r\n                  <div className=\"border-muted bg-background flex h-10 w-10 items-center justify-center rounded-md border\">\r\n                    {item.icon}\r\n                  </div>\r\n                  <div>\r\n                    <p className=\"font-medium\">{item.title}</p>\r\n                    <p className=\"text-muted-foreground text-xs\">\r\n                      {item.description}\r\n                    </p>\r\n                  </div>\r\n                </EditorCommandItem>\r\n              ))}\r\n            </EditorCommandList>\r\n          </EditorCommand>\r\n\r\n          <GenerativeMenuSwitch open={openAI} onOpenChange={setOpenAI}>\r\n            <Separator orientation=\"vertical\" />\r\n            <NodeSelector open={openNode} onOpenChange={setOpenNode} />\r\n            <Separator orientation=\"vertical\" />\r\n            <TextButtons />\r\n            <Separator orientation=\"vertical\" />\r\n            <ColorSelector open={openColor} onOpenChange={setOpenColor} />\r\n            <Separator orientation=\"vertical\" />\r\n            <LinkSelector open={openLink} onOpenChange={setOpenLink} />\r\n            <Separator orientation=\"vertical\" />\r\n            <MathSelector />\r\n          </GenerativeMenuSwitch>\r\n        </EditorContent>\r\n      </EditorRoot>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReportEditor;\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AA7BA;;;;;;;;;;;;;;;AAkCA,MAAM;AAEN,MAAM,aAAa;OAAI,6IAAA,CAAA,oBAAiB;IAAE,mJAAA,CAAA,eAAY;CAAC;AAOvD,MAAM,eAAe,CAAC,EAAE,OAAO,EAAE,gBAAgB,EAAqB;;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD;iCAAW,IAAM;;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,gEAAgE;IAChE,MAAM,sBAAsB,CAAC;QAC3B,MAAM,MAAM,IAAI,YAAY,eAAe,CAAC,SAAS;QACrD,IAAI,gBAAgB,CAAC,YAAY,OAAO,CAAC,CAAC;YACxC,aAAa;YACb,oGAAoG;YACpG,KAAK,gBAAgB,CAAC;QACxB;QACA,OAAO,IAAI,gBAAgB,iBAAiB,CAAC;IAC/C;IAEA,MAAM,mBAAmB,CAAA,GAAA,gPAAA,CAAA,uBAAoB,AAAD;+DAC1C,OAAO;YACL,iCAAiC;YACjC,2DAA2D;YAC3D,+BAA+B;YAC/B,oBAAoB;YACpB,2CAA2C;YAC3C,KAAK;YACL,sEAAsE;YACtE,+BAA+B;YAC/B,gBAAgB;YAChB,2CAA2C;YAC3C,KAAK;YACL,IAAI,kBAAkB;gBACpB,MAAM,WAAW,OAAO,OAAO,CAAC,QAAQ,CAAC,WAAW;gBACpD,iBAAiB;YACnB;YACA,cAAc;QAChB;8DACA;IAGF,oBAAoB;IACpB,kEAAkE;IAClE,yDAAyD;IACzD,kDAAkD;IAClD,UAAU;IAEV,IAAI,CAAC,gBAAgB,OAAO;IAE5B,qBACE,sSAAC;QAAI,WAAU;kBACb,cAAA,sSAAC,kQAAA,CAAA,aAAU;sBACT,cAAA,sSAAC,kQAAA,CAAA,gBAAa;gBACZ,mBAAmB;gBACnB,gBAAgB;gBAChB,YAAY;gBACZ,WAAU;gBACV,aAAa;oBACX,iBAAiB;wBACf,SAAS,CAAC,OAAO,QAAU,CAAA,GAAA,kQAAA,CAAA,0BAAuB,AAAD,EAAE;oBACrD;oBACA,aAAa,CAAC,MAAM,QAClB,CAAA,GAAA,kQAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,OAAO,iJAAA,CAAA,WAAQ;oBACxC,YAAY,CAAC,MAAM,OAAO,QAAQ,QAChC,CAAA,GAAA,kQAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,OAAO,OAAO,iJAAA,CAAA,WAAQ;oBAC9C,YAAY;wBACV,OACE;oBACJ;gBACF;gBACA,UAAU,CAAC,EAAE,MAAM,EAAE;oBACnB,iBAAiB;oBACjB,cAAc;gBAChB;gBACA,yBAAW,sSAAC,kQAAA,CAAA,eAAY;;;;;;kCAExB,sSAAC,kQAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,sSAAC,kQAAA,CAAA,qBAAkB;gCAAC,WAAU;0CAA6B;;;;;;0CAG3D,sSAAC,kQAAA,CAAA,oBAAiB;0CACf,mJAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAC,qBACpB,sSAAC,kQAAA,CAAA,oBAAiB;wCAChB,OAAO,KAAK,KAAK;wCACjB,WAAW,CAAC,MAAQ,KAAK,OAAO,GAAG;wCACnC,WAAU;;0DAGV,sSAAC;gDAAI,WAAU;0DACZ,KAAK,IAAI;;;;;;0DAEZ,sSAAC;;kEACC,sSAAC;wDAAE,WAAU;kEAAe,KAAK,KAAK;;;;;;kEACtC,sSAAC;wDAAE,WAAU;kEACV,KAAK,WAAW;;;;;;;;;;;;;uCARhB,KAAK,KAAK;;;;;;;;;;;;;;;;kCAgBvB,sSAAC,6KAAA,CAAA,UAAoB;wBAAC,MAAM;wBAAQ,cAAc;;0CAChD,sSAAC,wIAAA,CAAA,YAAS;gCAAC,aAAY;;;;;;0CACvB,sSAAC,gKAAA,CAAA,eAAY;gCAAC,MAAM;gCAAU,cAAc;;;;;;0CAC5C,sSAAC,wIAAA,CAAA,YAAS;gCAAC,aAAY;;;;;;0CACvB,sSAAC,+JAAA,CAAA,cAAW;;;;;0CACZ,sSAAC,wIAAA,CAAA,YAAS;gCAAC,aAAY;;;;;;0CACvB,sSAAC,iKAAA,CAAA,gBAAa;gCAAC,MAAM;gCAAW,cAAc;;;;;;0CAC9C,sSAAC,wIAAA,CAAA,YAAS;gCAAC,aAAY;;;;;;0CACvB,sSAAC,gKAAA,CAAA,eAAY;gCAAC,MAAM;gCAAU,cAAc;;;;;;0CAC5C,sSAAC,wIAAA,CAAA,YAAS;gCAAC,aAAY;;;;;;0CACvB,sSAAC,gKAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzB;GAvHM;;QAoBqB,gPAAA,CAAA,uBAAoB;;;KApBzC;uCAyHS", "debugId": null}}, {"offset": {"line": 7268, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/chat/components/research-report-block.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { useCallback, useRef } from \"react\";\r\n\r\nimport { LoadingAnimation } from \"~/components/deer-flow/loading-animation\";\r\nimport { Markdown } from \"~/components/deer-flow/markdown\";\r\nimport ReportEditor from \"~/components/editor\";\r\nimport { useReplay } from \"~/core/replay\";\r\nimport { useMessage, useStore } from \"~/core/store\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nexport function ResearchReportBlock({\r\n  className,\r\n  messageId,\r\n  editing,\r\n}: {\r\n  className?: string;\r\n  researchId: string;\r\n  messageId: string;\r\n  editing: boolean;\r\n}) {\r\n  const message = useMessage(messageId);\r\n  const { isReplay } = useReplay();\r\n  const handleMarkdownChange = useCallback(\r\n    (markdown: string) => {\r\n      if (message) {\r\n        message.content = markdown;\r\n        useStore.setState({\r\n          messages: new Map(useStore.getState().messages).set(\r\n            message.id,\r\n            message,\r\n          ),\r\n        });\r\n      }\r\n    },\r\n    [message],\r\n  );\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const isCompleted = message?.isStreaming === false && message?.content !== \"\";\r\n  // TODO: scroll to top when completed, but it's not working\r\n  // useEffect(() => {\r\n  //   if (isCompleted && contentRef.current) {\r\n  //     setTimeout(() => {\r\n  //       contentRef\r\n  //         .current!.closest(\"[data-radix-scroll-area-viewport]\")\r\n  //         ?.scrollTo({\r\n  //           top: 0,\r\n  //           behavior: \"smooth\",\r\n  //         });\r\n  //     }, 500);\r\n  //   }\r\n  // }, [isCompleted]);\r\n\r\n  return (\r\n    <div\r\n      ref={contentRef}\r\n      className={cn(\"relative flex flex-col pt-4 pb-8\", className)}\r\n    >\r\n      {!isReplay && isCompleted && editing ? (\r\n        <ReportEditor\r\n          content={message?.content}\r\n          onMarkdownChange={handleMarkdownChange}\r\n        />\r\n      ) : (\r\n        <>\r\n          <Markdown animated checkLinkCredibility>\r\n            {message?.content}\r\n          </Markdown>\r\n          {message?.isStreaming && <LoadingAnimation className=\"my-12\" />}\r\n        </>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;;;;;;;;;;AAEO,SAAS,oBAAoB,EAClC,SAAS,EACT,SAAS,EACT,OAAO,EAMR;;IACC,MAAM,UAAU,CAAA,GAAA,gIAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IAC7B,MAAM,uBAAuB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;iEACrC,CAAC;YACC,IAAI,SAAS;gBACX,QAAQ,OAAO,GAAG;gBAClB,gIAAA,CAAA,WAAQ,CAAC,QAAQ,CAAC;oBAChB,UAAU,IAAI,IAAI,gIAAA,CAAA,WAAQ,CAAC,QAAQ,GAAG,QAAQ,EAAE,GAAG,CACjD,QAAQ,EAAE,EACV;gBAEJ;YACF;QACF;gEACA;QAAC;KAAQ;IAEX,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,cAAc,SAAS,gBAAgB,SAAS,SAAS,YAAY;IAC3E,2DAA2D;IAC3D,oBAAoB;IACpB,6CAA6C;IAC7C,yBAAyB;IACzB,mBAAmB;IACnB,iEAAiE;IACjE,uBAAuB;IACvB,oBAAoB;IACpB,gCAAgC;IAChC,cAAc;IACd,eAAe;IACf,MAAM;IACN,qBAAqB;IAErB,qBACE,sSAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;kBAEjD,CAAC,YAAY,eAAe,wBAC3B,sSAAC,wIAAA,CAAA,UAAY;YACX,SAAS,SAAS;YAClB,kBAAkB;;;;;iCAGpB;;8BACE,sSAAC,iJAAA,CAAA,WAAQ;oBAAC,QAAQ;oBAAC,oBAAoB;8BACpC,SAAS;;;;;;gBAEX,SAAS,6BAAe,sSAAC,6JAAA,CAAA,mBAAgB;oBAAC,WAAU;;;;;;;;;;;;;AAK/D;GA9DgB;;QAUE,gIAAA,CAAA,aAAU;QACL,iIAAA,CAAA,YAAS;;;KAXhB", "debugId": null}}, {"offset": {"line": 7377, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/chat/components/research-block.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\nimport { Check, Copy, Headphones, Pencil, Undo2, X } from \"lucide-react\";\r\nimport { useCallback, useEffect, useState } from \"react\";\r\n\r\nimport { ScrollContainer } from \"~/components/deer-flow/scroll-container\";\r\nimport { Tooltip } from \"~/components/deer-flow/tooltip\";\r\nimport { Button } from \"~/components/ui/button\";\r\nimport { Card } from \"~/components/ui/card\";\r\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from \"~/components/ui/tabs\";\r\nimport { useReplay } from \"~/core/replay\";\r\nimport { closeResearch, listenToPodcast, useStore } from \"~/core/store\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport { ResearchActivitiesBlock } from \"./research-activities-block\";\r\nimport { ResearchReportBlock } from \"./research-report-block\";\r\n\r\nexport function ResearchBlock({\r\n  className,\r\n  researchId = null,\r\n}: {\r\n  className?: string;\r\n  researchId: string | null;\r\n}) {\r\n  const reportId = useStore((state) =>\r\n    researchId ? state.researchReportIds.get(researchId) : undefined,\r\n  );\r\n  const [activeTab, setActiveTab] = useState(\"activities\");\r\n  const hasReport = useStore((state) =>\r\n    researchId ? state.researchReportIds.has(researchId) : false,\r\n  );\r\n  const reportStreaming = useStore((state) =>\r\n    reportId ? (state.messages.get(reportId)?.isStreaming ?? false) : false,\r\n  );\r\n  const { isReplay } = useReplay();\r\n  useEffect(() => {\r\n    if (hasReport) {\r\n      setActiveTab(\"report\");\r\n    }\r\n  }, [hasReport]);\r\n\r\n  const handleGeneratePodcast = useCallback(async () => {\r\n    if (!researchId) {\r\n      return;\r\n    }\r\n    await listenToPodcast(researchId);\r\n  }, [researchId]);\r\n\r\n  const [editing, setEditing] = useState(false);\r\n  const [copied, setCopied] = useState(false);\r\n  const handleCopy = useCallback(() => {\r\n    if (!reportId) {\r\n      return;\r\n    }\r\n    const report = useStore.getState().messages.get(reportId);\r\n    if (!report) {\r\n      return;\r\n    }\r\n    void navigator.clipboard.writeText(report.content);\r\n    setCopied(true);\r\n    setTimeout(() => {\r\n      setCopied(false);\r\n    }, 1000);\r\n  }, [reportId]);\r\n\r\n  const handleEdit = useCallback(() => {\r\n    setEditing((editing) => !editing);\r\n  }, []);\r\n\r\n  // When the research id changes, set the active tab to activities\r\n  useEffect(() => {\r\n    if (!hasReport) {\r\n      setActiveTab(\"activities\");\r\n    }\r\n  }, [hasReport, researchId]);\r\n\r\n  return (\r\n    <div className={cn(\"h-full w-full\", className)}>\r\n      <Card className={cn(\"relative h-full w-full pt-4\", className)}>\r\n        <div className=\"absolute right-4 flex h-9 items-center justify-center\">\r\n          {hasReport && !reportStreaming && (\r\n            <>\r\n              <Tooltip title=\"Generate podcast\">\r\n                <Button\r\n                  className=\"text-gray-400\"\r\n                  size=\"icon\"\r\n                  variant=\"ghost\"\r\n                  disabled={isReplay}\r\n                  onClick={handleGeneratePodcast}\r\n                >\r\n                  <Headphones />\r\n                </Button>\r\n              </Tooltip>\r\n              <Tooltip title=\"Edit\">\r\n                <Button\r\n                  className=\"text-gray-400\"\r\n                  size=\"icon\"\r\n                  variant=\"ghost\"\r\n                  disabled={isReplay}\r\n                  onClick={handleEdit}\r\n                >\r\n                  {editing ? <Undo2 /> : <Pencil />}\r\n                </Button>\r\n              </Tooltip>\r\n              <Tooltip title=\"Copy\">\r\n                <Button\r\n                  className=\"text-gray-400\"\r\n                  size=\"icon\"\r\n                  variant=\"ghost\"\r\n                  onClick={handleCopy}\r\n                >\r\n                  {copied ? <Check /> : <Copy />}\r\n                </Button>\r\n              </Tooltip>\r\n            </>\r\n          )}\r\n          <Tooltip title=\"Close\">\r\n            <Button\r\n              className=\"text-gray-400\"\r\n              size=\"sm\"\r\n              variant=\"ghost\"\r\n              onClick={() => {\r\n                closeResearch();\r\n              }}\r\n            >\r\n              <X />\r\n            </Button>\r\n          </Tooltip>\r\n        </div>\r\n        <Tabs\r\n          className=\"flex h-full w-full flex-col\"\r\n          value={activeTab}\r\n          onValueChange={(value) => setActiveTab(value)}\r\n        >\r\n          <div className=\"flex w-full justify-center\">\r\n            <TabsList className=\"\">\r\n              <TabsTrigger\r\n                className=\"px-8\"\r\n                value=\"report\"\r\n                disabled={!hasReport}\r\n              >\r\n                Report\r\n              </TabsTrigger>\r\n              <TabsTrigger className=\"px-8\" value=\"activities\">\r\n                Activities\r\n              </TabsTrigger>\r\n            </TabsList>\r\n          </div>\r\n          <TabsContent\r\n            className=\"h-full min-h-0 flex-grow px-8\"\r\n            value=\"report\"\r\n            forceMount\r\n            hidden={activeTab !== \"report\"}\r\n          >\r\n            <ScrollContainer\r\n              className=\"px-5pb-20 h-full\"\r\n              scrollShadowColor=\"var(--card)\"\r\n              autoScrollToBottom={!hasReport || reportStreaming}\r\n            >\r\n              {reportId && researchId && (\r\n                <ResearchReportBlock\r\n                  className=\"mt-4\"\r\n                  researchId={researchId}\r\n                  messageId={reportId}\r\n                  editing={editing}\r\n                />\r\n              )}\r\n            </ScrollContainer>\r\n          </TabsContent>\r\n          <TabsContent\r\n            className=\"h-full min-h-0 flex-grow px-8\"\r\n            value=\"activities\"\r\n            forceMount\r\n            hidden={activeTab !== \"activities\"}\r\n          >\r\n            <ScrollContainer\r\n              className=\"h-full\"\r\n              scrollShadowColor=\"var(--card)\"\r\n              autoScrollToBottom={!hasReport || reportStreaming}\r\n            >\r\n              {researchId && (\r\n                <ResearchActivitiesBlock\r\n                  className=\"mt-4\"\r\n                  researchId={researchId}\r\n                />\r\n              )}\r\n            </ScrollContainer>\r\n          </TabsContent>\r\n        </Tabs>\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAE/B;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAEA;AACA;;;;;;;;;;;;;;;AAEO,SAAS,cAAc,EAC5B,SAAS,EACT,aAAa,IAAI,EAIlB;;IACC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;4CAAE,CAAC,QACzB,aAAa,MAAM,iBAAiB,CAAC,GAAG,CAAC,cAAc;;IAEzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,YAAY,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;6CAAE,CAAC,QAC1B,aAAa,MAAM,iBAAiB,CAAC,GAAG,CAAC,cAAc;;IAEzD,MAAM,kBAAkB,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;mDAAE,CAAC,QAChC,WAAY,MAAM,QAAQ,CAAC,GAAG,CAAC,WAAW,eAAe,QAAS;;IAEpE,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,YAAS,AAAD;IAC7B,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,WAAW;gBACb,aAAa;YACf;QACF;kCAAG;QAAC;KAAU;IAEd,MAAM,wBAAwB,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;4DAAE;YACxC,IAAI,CAAC,YAAY;gBACf;YACF;YACA,MAAM,CAAA,GAAA,gIAAA,CAAA,kBAAe,AAAD,EAAE;QACxB;2DAAG;QAAC;KAAW;IAEf,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,sQAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;iDAAE;YAC7B,IAAI,CAAC,UAAU;gBACb;YACF;YACA,MAAM,SAAS,gIAAA,CAAA,WAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC;YAChD,IAAI,CAAC,QAAQ;gBACX;YACF;YACA,KAAK,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,OAAO;YACjD,UAAU;YACV;yDAAW;oBACT,UAAU;gBACZ;wDAAG;QACL;gDAAG;QAAC;KAAS;IAEb,MAAM,aAAa,CAAA,GAAA,sQAAA,CAAA,cAAW,AAAD;iDAAE;YAC7B;yDAAW,CAAC,UAAY,CAAC;;QAC3B;gDAAG,EAAE;IAEL,iEAAiE;IACjE,CAAA,GAAA,sQAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW;gBACd,aAAa;YACf;QACF;kCAAG;QAAC;QAAW;KAAW;IAE1B,qBACE,sSAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBAClC,cAAA,sSAAC,mIAAA,CAAA,OAAI;YAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;;8BACjD,sSAAC;oBAAI,WAAU;;wBACZ,aAAa,CAAC,iCACb;;8CACE,sSAAC,gJAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,UAAU;wCACV,SAAS;kDAET,cAAA,sSAAC,qSAAA,CAAA,aAAU;;;;;;;;;;;;;;;8CAGf,sSAAC,gJAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,UAAU;wCACV,SAAS;kDAER,wBAAU,sSAAC,+RAAA,CAAA,QAAK;;;;iEAAM,sSAAC,6RAAA,CAAA,SAAM;;;;;;;;;;;;;;;8CAGlC,sSAAC,gJAAA,CAAA,UAAO;oCAAC,OAAM;8CACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;wCACL,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,SAAS;kDAER,uBAAS,sSAAC,2RAAA,CAAA,QAAK;;;;iEAAM,sSAAC,yRAAA,CAAA,OAAI;;;;;;;;;;;;;;;;;sCAKnC,sSAAC,gJAAA,CAAA,UAAO;4BAAC,OAAM;sCACb,cAAA,sSAAC,qIAAA,CAAA,SAAM;gCACL,WAAU;gCACV,MAAK;gCACL,SAAQ;gCACR,SAAS;oCACP,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;gCACd;0CAEA,cAAA,sSAAC,mRAAA,CAAA,IAAC;;;;;;;;;;;;;;;;;;;;;8BAIR,sSAAC,mIAAA,CAAA,OAAI;oBACH,WAAU;oBACV,OAAO;oBACP,eAAe,CAAC,QAAU,aAAa;;sCAEvC,sSAAC;4BAAI,WAAU;sCACb,cAAA,sSAAC,mIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,sSAAC,mIAAA,CAAA,cAAW;wCACV,WAAU;wCACV,OAAM;wCACN,UAAU,CAAC;kDACZ;;;;;;kDAGD,sSAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;wCAAO,OAAM;kDAAa;;;;;;;;;;;;;;;;;sCAKrD,sSAAC,mIAAA,CAAA,cAAW;4BACV,WAAU;4BACV,OAAM;4BACN,UAAU;4BACV,QAAQ,cAAc;sCAEtB,cAAA,sSAAC,4JAAA,CAAA,kBAAe;gCACd,WAAU;gCACV,mBAAkB;gCAClB,oBAAoB,CAAC,aAAa;0CAEjC,YAAY,4BACX,sSAAC,mKAAA,CAAA,sBAAmB;oCAClB,WAAU;oCACV,YAAY;oCACZ,WAAW;oCACX,SAAS;;;;;;;;;;;;;;;;sCAKjB,sSAAC,mIAAA,CAAA,cAAW;4BACV,WAAU;4BACV,OAAM;4BACN,UAAU;4BACV,QAAQ,cAAc;sCAEtB,cAAA,sSAAC,4JAAA,CAAA,kBAAe;gCACd,WAAU;gCACV,mBAAkB;gCAClB,oBAAoB,CAAC,aAAa;0CAEjC,4BACC,sSAAC,uKAAA,CAAA,0BAAuB;oCACtB,WAAU;oCACV,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9B;GA/KgB;;QAOG,gIAAA,CAAA,WAAQ;QAIP,gIAAA,CAAA,WAAQ;QAGF,gIAAA,CAAA,WAAQ;QAGX,iIAAA,CAAA,YAAS;;;KAjBhB", "debugId": null}}, {"offset": {"line": 7740, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/projects/kb_deepresearch/deer-flow-extend/web/src/app/chat/main.tsx"], "sourcesContent": ["// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates\r\n// SPDX-License-Identifier: MIT\r\n\r\n\"use client\";\r\n\r\nimport { useMemo } from \"react\";\r\n\r\nimport { useStore } from \"~/core/store\";\r\nimport { cn } from \"~/lib/utils\";\r\n\r\nimport { MessagesBlock } from \"./components/messages-block\";\r\nimport { ResearchBlock } from \"./components/research-block\";\r\n\r\nexport default function Main() {\r\n  const openResearchId = useStore((state) => state.openResearchId);\r\n  const doubleColumnMode = useMemo(\r\n    () => openResearchId !== null,\r\n    [openResearchId],\r\n  );\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex h-full w-full justify-center-safe px-4 pt-12 pb-4\",\r\n        doubleColumnMode && \"gap-8\",\r\n      )}\r\n    >\r\n      <MessagesBlock\r\n        className={cn(\r\n          \"shrink-0 transition-all duration-300 ease-out\",\r\n          !doubleColumnMode &&\r\n            `w-[768px] translate-x-[min(max(calc((100vw-538px)*0.75),575px)/2,960px/2)]`,\r\n          doubleColumnMode && `w-[538px]`,\r\n        )}\r\n      />\r\n      <ResearchBlock\r\n        className={cn(\r\n          \"w-[min(max(calc((100vw-538px)*0.75),575px),960px)] pb-4 transition-all duration-300 ease-out\",\r\n          !doubleColumnMode && \"scale-0\",\r\n          doubleColumnMode && \"\",\r\n        )}\r\n        researchId={openResearchId}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,0DAA0D;AAC1D,+BAA+B;;;;;AAI/B;AAEA;AAAA;AACA;AAEA;AACA;;;AARA;;;;;;AAUe,SAAS;;IACtB,MAAM,iBAAiB,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;yCAAE,CAAC,QAAU,MAAM,cAAc;;IAC/D,MAAM,mBAAmB,CAAA,GAAA,sQAAA,CAAA,UAAO,AAAD;0CAC7B,IAAM,mBAAmB;yCACzB;QAAC;KAAe;IAElB,qBACE,sSAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA,oBAAoB;;0BAGtB,sSAAC,yJAAA,CAAA,gBAAa;gBACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,CAAC,oBACC,CAAC,0EAA0E,CAAC,EAC9E,oBAAoB,CAAC,SAAS,CAAC;;;;;;0BAGnC,sSAAC,yJAAA,CAAA,gBAAa;gBACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gGACA,CAAC,oBAAoB,WACrB,oBAAoB;gBAEtB,YAAY;;;;;;;;;;;;AAIpB;GA/BwB;;QACC,gIAAA,CAAA,WAAQ;;;KADT", "debugId": null}}]}