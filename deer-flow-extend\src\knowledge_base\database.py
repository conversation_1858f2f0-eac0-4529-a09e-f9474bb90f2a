# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import logging
import os
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime
import asyncpg
import json

logger = logging.getLogger(__name__)


class KnowledgeBaseDB:
    """Database operations for knowledge base management."""
    
    def __init__(self):
        self.connection_pool = None
        self.schema = "kb_main"
    
    async def initialize(self):
        """Initialize database connection pool."""
        if self.connection_pool:
            return
            
        database_url = (
            f"postgresql://{os.getenv('KB_DB_USER', 'postgres')}:"
            f"{os.getenv('KB_DB_PASSWORD', 'password')}@"
            f"{os.getenv('KB_DB_HOST', 'localhost')}:"
            f"{os.getenv('KB_DB_PORT', '5432')}/"
            f"{os.getenv('KB_DB_NAME', 'deerflow')}"
        )
        
        try:
            self.connection_pool = await asyncpg.create_pool(
                database_url,
                min_size=2,
                max_size=10,
                command_timeout=60
            )
            logger.info("Database connection pool initialized")
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    async def close(self):
        """Close database connection pool."""
        if self.connection_pool:
            await self.connection_pool.close()
            self.connection_pool = None
    
    async def store_article_with_chunks(
        self,
        url: str,
        title: str,
        summary: str,
        industry: str,
        chunks: List[Dict[str, Any]]
    ) -> bool:
        """
        Store an article and its chunks in the database.
        
        Args:
            url: Article URL
            title: Article title
            summary: Article summary
            industry: Industry classification
            chunks: List of chunk data with text, summary, and embeddings
            
        Returns:
            True if successful, False otherwise
        """
        if not self.connection_pool:
            await self.initialize()
        
        async with self.connection_pool.acquire() as conn:
            async with conn.transaction():
                try:
                    # Insert article
                    article_id = await conn.fetchval(
                        f"""
                        INSERT INTO {self.schema}.kb_crawled_articles 
                        (url, title, summary, industry, source_type)
                        VALUES ($1, $2, $3, $4, 'web')
                        RETURNING id
                        """,
                        url, title, summary, industry
                    )
                    
                    # Insert chunks
                    for chunk in chunks:
                        await conn.execute(
                            f"""
                            INSERT INTO {self.schema}.kb_crawled_article_chunks
                            (article_id, chunk_text, chunk_index, chunk_summary, 
                             token_count, embedding, industry)
                            VALUES ($1, $2, $3, $4, $5, $6, $7)
                            """,
                            article_id,
                            chunk["chunk_text"],
                            chunk["chunk_index"],
                            chunk["chunk_summary"],
                            chunk["token_count"],
                            chunk["embedding"],
                            industry
                        )
                    
                    logger.info(f"Stored article {article_id} with {len(chunks)} chunks")
                    return True
                    
                except Exception as e:
                    logger.error(f"Error storing article {url}: {e}")
                    return False
    
    async def check_url_exists(self, url: str) -> bool:
        """Check if a URL already exists in the database."""
        if not self.connection_pool:
            await self.initialize()
            
        async with self.connection_pool.acquire() as conn:
            result = await conn.fetchval(
                f"SELECT 1 FROM {self.schema}.kb_crawled_articles WHERE url = $1",
                url
            )
            return result is not None
    
    async def get_article_count_by_industry(self) -> Dict[str, int]:
        """Get article counts grouped by industry."""
        if not self.connection_pool:
            await self.initialize()
            
        async with self.connection_pool.acquire() as conn:
            rows = await conn.fetch(
                f"""
                SELECT industry, COUNT(*) as count 
                FROM {self.schema}.kb_crawled_articles 
                GROUP BY industry
                """
            )
            return {row["industry"]: row["count"] for row in rows}
    
    async def get_total_article_count(self) -> int:
        """Get total number of articles in the knowledge base."""
        if not self.connection_pool:
            await self.initialize()
            
        async with self.connection_pool.acquire() as conn:
            result = await conn.fetchval(
                f"SELECT COUNT(*) FROM {self.schema}.kb_crawled_articles"
            )
            return result or 0


# Global database instance
kb_db = KnowledgeBaseDB()