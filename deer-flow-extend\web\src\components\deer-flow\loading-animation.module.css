@keyframes bouncing-animation {
  to {
    opacity: 0.1;
    transform: translateY(-8px);
  }
}

.loadingAnimation {
  display: flex;
}

.loadingAnimation > div {
  width: 8px;
  height: 8px;
  margin: 2px 4px;
  border-radius: 50%;
  background-color: #a3a1a1;
  opacity: 1;
  animation: bouncing-animation 0.5s infinite alternate;
}

.loadingAnimation.sm > div {
  width: 6px;
  height: 6px;
  margin: 1px 2px;
}

.loadingAnimation > div:nth-child(2) {
  animation-delay: 0.2s;
}

.loadingAnimation > div:nth-child(3) {
  animation-delay: 0.4s;
}
