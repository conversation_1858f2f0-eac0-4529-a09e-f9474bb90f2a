from typing import Dict, List, Optional, Set, Any
from langgraph.graph import MessagesState
from src.prompts.planner_model import Plan


class URLStatus:
    """URL processing status constants."""
    PENDING = "pending"
    PROCESSING = "processing" 
    PROCESSED = "processed"
    FAILED = "failed"
    IRRELEVANT = "irrelevant"


class KnowledgeBaseState(MessagesState):
    """State for the knowledge base building process."""
    
    # === Core Planning ===
    current_plan: Optional[Plan] = None
    plan_iterations: int = 0
    
    # === Knowledge Acquisition Goals ===
    target_article_count: int = 10
    target_topics: List[str] = []
    target_industries: List[str] = []
    locale: str = "en-US"
    
    # === URL Management ===
    url_candidates: Dict[str, Dict[str, Any]] = {}  # URL -> metadata
    pending_urls: Set[str] = set()
    processing_urls: Set[str] = set()
    processed_urls: Set[str] = set()
    failed_urls: Set[str] = set()
    
    # === Progress Tracking ===
    processed_article_count: int = 0
    article_counts_by_topic: Dict[str, int] = {}
    article_counts_by_industry: Dict[str, int] = {}
    
    # === Current Processing Context ===
    current_url: Optional[str] = None
    current_topic: Optional[str] = None
    current_industry: Optional[str] = None
    
    # === Processed Content Pipeline ===
    raw_content: Optional[str] = None
    processed_chunks: List[Dict[str, Any]] = []
    
    # === Error Handling ===
    last_error: Optional[str] = None
    
    # === Database ===
    db_schema: str = "kb_main"