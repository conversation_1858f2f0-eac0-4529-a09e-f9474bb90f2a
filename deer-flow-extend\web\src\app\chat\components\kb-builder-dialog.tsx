// web/src/app/chat/components/kb-builder-dialog.tsx
// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import { useState, useCallback, useMemo } from "react";
import { Plus, X, Database, BookOpen } from "lucide-react";
import { motion } from "framer-motion";

import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Badge } from "~/components/ui/badge";
import { Slider } from "~/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { Tooltip } from "~/components/deer-flow/tooltip";
import { useStore } from "~/core/store";

const COMMON_TOPICS = [
  "artificial intelligence",
  "machine learning", 
  "blockchain",
  "cybersecurity",
  "cloud computing",
  "data science",
  "robotics",
  "quantum computing",
  "biotechnology",
  "renewable energy",
];

const INDUSTRIES = [
  "technology",
  "healthcare", 
  "finance",
  "manufacturing",
  "retail",
  "transportation",
  "energy",
  "education",
  "government",
];

export function KnowledgeBaseBuilderDialog() {
  const [open, setOpen] = useState(false);
  const [topics, setTopics] = useState<string[]>([]);
  const [newTopic, setNewTopic] = useState("");
  const [industries, setIndustries] = useState<string[]>([]);
  const [targetCount, setTargetCount] = useState([20]);
  const [locale, setLocale] = useState("en-US");
  const [maxSteps, setMaxSteps] = useState([5]);
  const [maxResults, setMaxResults] = useState([5]);

  // Use useStore with useCallback to prevent infinite re-renders
  const kbState = useStore(useCallback((state) => state.knowledgeBase, []));
  const startBuild = useStore(useCallback((state) => state.startKnowledgeBaseBuild, []));

  // Memoize the building state to prevent unnecessary re-renders
  const isBuilding = useMemo(() => kbState.isBuilding, [kbState.isBuilding]);

  const handleAddTopic = useCallback(() => {
    if (newTopic.trim() && !topics.includes(newTopic.trim())) {
      setTopics(prev => [...prev, newTopic.trim()]);
      setNewTopic("");
    }
  }, [newTopic, topics]);

  const handleRemoveTopic = useCallback((topic: string) => {
    setTopics(prev => prev.filter((t) => t !== topic));
  }, []);

  const handleAddCommonTopic = useCallback((topic: string) => {
    if (!topics.includes(topic)) {
      setTopics(prev => [...prev, topic]);
    }
  }, [topics]);

  const handleToggleIndustry = useCallback((industry: string) => {
    setIndustries(prev => {
      if (prev.includes(industry)) {
        return prev.filter((i) => i !== industry);
      } else {
        return [...prev, industry];
      }
    });
  }, []);

  const handleSubmit = useCallback(async () => {
    if (topics.length === 0) return;

    try {
      await startBuild({
        topics,
        target_article_count: targetCount[0] ?? 20,
        industries: industries.length > 0 ? industries : undefined,
        locale,
        max_step_num: maxSteps[0],
        max_search_results: maxResults[0],
      });
      setOpen(false);
    } catch (error) {
      console.error("Failed to start KB build:", error);
    }
  }, [topics, targetCount, industries, locale, maxSteps, maxResults, startBuild]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Tooltip title="Build Knowledge Base">
          <Button
            variant="outline"
            size="icon"
            disabled={isBuilding}
            className="relative"
          >
            <Database size={16} />
            {isBuilding && (
              <motion.div
                className="absolute inset-0 rounded-md border-2 border-blue-500"
                animate={{ opacity: [0.5, 1, 0.5] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              />
            )}
          </Button>
        </Tooltip>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen size={20} />
            Build Knowledge Base
          </DialogTitle>
          <DialogDescription>
            Create a comprehensive knowledge base by automatically discovering,
            processing, and storing high-quality articles on your specified topics.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Topics Section */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Topics *</Label>
            
            {/* Current Topics */}
            {topics.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {topics.map((topic) => (
                  <Badge key={topic} variant="secondary" className="px-3 py-1">
                    {topic}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-1 h-4 w-4 p-0 hover:bg-transparent"
                      onClick={() => handleRemoveTopic(topic)}
                    >
                      <X size={12} />
                    </Button>
                  </Badge>
                ))}
              </div>
            )}

            {/* Add Topic Input */}
            <div className="flex gap-2">
              <Input
                placeholder="Enter a topic (e.g., artificial intelligence)"
                value={newTopic}
                onChange={(e) => setNewTopic(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleAddTopic()}
              />
              <Button onClick={handleAddTopic} disabled={!newTopic.trim()}>
                <Plus size={16} />
              </Button>
            </div>

            {/* Common Topics */}
            <div className="space-y-2">
              <Label className="text-sm text-muted-foreground">
                Quick Add:
              </Label>
              <div className="flex flex-wrap gap-2">
                {COMMON_TOPICS.map((topic) => (
                  <Badge
                    key={topic}
                    variant="outline"
                    className={`cursor-pointer transition-colors hover:bg-accent ${
                      topics.includes(topic) ? "bg-accent" : ""
                    }`}
                    onClick={() => handleAddCommonTopic(topic)}
                  >
                    {topic}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          {/* Industries Section */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Industries (Optional)</Label>
            <div className="flex flex-wrap gap-2">
              {INDUSTRIES.map((industry) => (
                <Badge
                  key={industry}
                  variant={industries.includes(industry) ? "default" : "outline"}
                  className="cursor-pointer transition-colors hover:bg-accent"
                  onClick={() => handleToggleIndustry(industry)}
                >
                  {industry}
                </Badge>
              ))}
            </div>
          </div>

          {/* Target Article Count */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">Target Articles</Label>
              <span className="text-sm font-mono text-muted-foreground">
                {targetCount[0]} articles
              </span>
            </div>
            <Slider
              value={targetCount}
              onValueChange={setTargetCount}
              max={100}
              min={5}
              step={5}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>5</span>
              <span>50</span>
              <span>100</span>
            </div>
          </div>

          {/* Advanced Settings */}
          <div className="space-y-4 rounded-lg border p-4">
            <Label className="text-sm font-medium text-muted-foreground">
              Advanced Settings
            </Label>
            
            <div className="grid grid-cols-2 gap-4">
              {/* Locale */}
              <div className="space-y-2">
                <Label className="text-sm">Language</Label>
                <Select value={locale} onValueChange={setLocale}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en-US">English (US)</SelectItem>
                    <SelectItem value="zh-CN">中文 (简体)</SelectItem>
                    <SelectItem value="es-ES">Español</SelectItem>
                    <SelectItem value="fr-FR">Français</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Max Planning Steps */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm">Planning Steps</Label>
                  <span className="text-xs text-muted-foreground">
                    {maxSteps[0]}
                  </span>
                </div>
                <Slider
                  value={maxSteps}
                  onValueChange={setMaxSteps}
                  max={10}
                  min={3}
                  step={1}
                  className="w-full"
                />
              </div>
            </div>

            {/* Search Results */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-sm">Search Results per Query</Label>
                <span className="text-xs text-muted-foreground">
                  {maxResults[0]}
                </span>
              </div>
              <Slider
                value={maxResults}
                onValueChange={setMaxResults}
                max={20}
                min={3}
                step={1}
                className="w-full"
              />
            </div>
          </div>

          {/* Error Display */}
          {kbState.lastError && (
            <div className="rounded-lg border border-red-200 bg-red-50 p-3 text-sm text-red-600">
              <strong>Error:</strong> {kbState.lastError}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={topics.length === 0 || isBuilding}
              className="min-w-[120px]"
            >
              {isBuilding ? (
                <motion.div
                  className="h-4 w-4 rounded-full border-2 border-white border-t-transparent"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                />
              ) : (
                <>
                  <Database size={16} className="mr-2" />
                  Build KB
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}