import logging
from typing import List, Optional, Dict, Any

from langgraph.graph import END, START, StateGraph
from langgraph.checkpoint.memory import MemorySaver

from .state import KnowledgeBaseState
from .nodes import (
    kb_planner_node,
    kb_research_team_node,
    kb_researcher_node,
    kb_processor_node,
    kb_injector_node,
    get_kb_status
)
from ..database import kb_db

logger = logging.getLogger(__name__)


def build_kb_graph(with_memory: bool = False):
    """
    Build and return the knowledge base building graph.
    
    Args:
        with_memory: Whether to include persistent memory for conversations
        
    Returns:
        Compiled LangGraph workflow
    """
    logger.info("Building knowledge base graph")
    
    # Create state graph
    builder = StateGraph(KnowledgeBaseState)
    
    # Add nodes
    builder.add_node("kb_planner", kb_planner_node)
    builder.add_node("kb_research_team", kb_research_team_node)
    builder.add_node("kb_researcher", kb_researcher_node)
    builder.add_node("kb_processor", kb_processor_node)
    builder.add_node("kb_injector", kb_injector_node)
    
    # Define edges
    builder.add_edge(START, "kb_planner")
    
    # Planner can go to research team or end
    builder.add_conditional_edges(
        "kb_planner",
        lambda state: "kb_research_team" if (
            state.get("current_plan") and 
            state["current_plan"].steps and 
            not state["current_plan"].has_enough_context and
            state["processed_article_count"] < state["target_article_count"]
        ) else "__end__",
        {
            "kb_research_team": "kb_research_team",
            "__end__": END
        }
    )
    
    # Research team coordinates between researcher and planner
    builder.add_conditional_edges(
        "kb_research_team",
        lambda state: (
            "kb_researcher" if (
                state.get("current_plan") and 
                state["current_plan"].steps and
                state["processed_article_count"] < state["target_article_count"] and
                (state["pending_urls"] or any(not getattr(step, "execution_res", None) for step in state["current_plan"].steps))
            ) else "kb_planner"
        ),
        {
            "kb_researcher": "kb_researcher", 
            "kb_planner": "kb_planner"
        }
    )
    
    # Researcher can go to processor or back to research team
    builder.add_conditional_edges(
        "kb_researcher",
        lambda state: "kb_processor" if state.get("current_url") else "kb_research_team",
        {
            "kb_processor": "kb_processor",
            "kb_research_team": "kb_research_team"
        }
    )
    
    # Processor goes to injector or back to research team
    builder.add_conditional_edges(
        "kb_processor", 
        lambda state: "kb_injector" if state.get("processed_chunks") else "kb_research_team",
        {
            "kb_injector": "kb_injector",
            "kb_research_team": "kb_research_team"
        }
    )
    
    # Injector always goes back to research team
    builder.add_edge("kb_injector", "kb_research_team")
    
    # Compile graph
    if with_memory:
        memory = MemorySaver()
        return builder.compile(checkpointer=memory)
    else:
        return builder.compile()


async def run_kb_builder(
    topics: List[str],
    target_article_count: int = 10,
    industries: Optional[List[str]] = None,
    locale: str = "en-US",
    config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Run the knowledge base builder workflow.
    
    Args:
        topics: List of topics to build knowledge about
        target_article_count: Number of articles to acquire
        industries: List of industries to focus on (optional)
        locale: Language locale for content
        config: Additional configuration
        
    Returns:
        Final workflow results including status and statistics
    """
    logger.info(f"Starting KB builder for topics: {topics}")
    
    # Initialize database
    await kb_db.initialize()
    
    try:
        # Create initial state
        initial_state = KnowledgeBaseState(
            messages=[],
            target_topics=topics,
            target_article_count=target_article_count,
            target_industries=industries or [],
            locale=locale,
            current_plan=None,
            plan_iterations=0,
            url_candidates={},
            pending_urls=set(),
            processing_urls=set(),
            processed_urls=set(),
            failed_urls=set(),
            article_counts_by_topic={},
            article_counts_by_industry={},
            processed_article_count=0,
            processed_chunks=[],
            last_error=None
        )
        
        # Build and run graph
        graph = build_kb_graph()
        
        runtime_config = config or {}
        runtime_config.setdefault("configurable", {
            "max_step_num": 5,
            "max_search_results": 5
        })
        
        # Track progress
        status_updates = []
        final_state = None
        
        # Stream workflow execution
        async for event in graph.astream(
            input=initial_state,
            config=runtime_config,
            stream_mode="updates"
        ):
            for node_name, node_output in event.items():
                if node_name != "__end__":
                    status = get_kb_status(node_output)
                    status_updates.append({
                        "node": node_name,
                        "timestamp": node_output.get("messages", [])[-1].content if node_output.get("messages") else "",
                        "status": status
                    })
                    logger.info(f"KB Builder progress at '{node_name}': {status['processed_article_count']}/{status['target_article_count']} articles")
                    final_state = node_output
        
        # Get final status
        if final_state:
            final_status = get_kb_status(final_state)
        else:
            # Fallback: invoke once more to get final state
            final_state = await graph.ainvoke(input=initial_state, config=runtime_config)
            final_status = get_kb_status(final_state)
        
        logger.info(f"KB Builder completed: {final_status['processed_article_count']} articles acquired")
        
        return {
            "success": True,
            "final_state": final_state,
            "final_status": final_status,
            "status_updates": status_updates,
            "message": f"Successfully acquired {final_status['processed_article_count']} articles"
        }
        
    except Exception as e:
        logger.error(f"Error in KB builder workflow: {e}")
        return {
            "success": False,
            "error": str(e),
            "final_status": None,
            "status_updates": status_updates if 'status_updates' in locals() else []
        }
    
    finally:
        # Clean up database connection
        await kb_db.close()


# Create default graph instance
kb_graph = build_kb_graph()


if __name__ == "__main__":
    import asyncio
    from dotenv import load_dotenv
    
    load_dotenv()
    logging.basicConfig(level=logging.INFO)
    
    async def test_kb_builder():
        """Test the knowledge base builder."""
        result = await run_kb_builder(
            topics=["artificial intelligence", "machine learning"],
            target_article_count=5,
            industries=["technology"],
            locale="en-US"
        )
        
        print("KB Builder Result:")
        print(f"Success: {result['success']}")
        if result['success']:
            print(f"Articles acquired: {result['final_status']['processed_article_count']}")
            print(f"By topic: {result['final_status']['article_counts_by_topic']}")
            print(f"By industry: {result['final_status']['article_counts_by_industry']}")
        else:
            print(f"Error: {result['error']}")
    
    asyncio.run(test_kb_builder())