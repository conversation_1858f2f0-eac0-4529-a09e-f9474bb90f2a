# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import base64
import json
import logging
import os
import re
import time
from typing import List, cast
from uuid import uuid4

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import Response, StreamingResponse
from langchain_core.messages import AIMessageChunk, ToolMessage, BaseMessage, HumanMessage
from langgraph.types import Command

from src.graph.builder import build_graph_with_memory
from src.podcast.graph.builder import build_graph as build_podcast_graph
from src.ppt.graph.builder import build_graph as build_ppt_graph
from src.prose.graph.builder import build_graph as build_prose_graph
from src.server.chat_request import (
    ChatMessage,
    ChatRequest,
    GeneratePodcastRequest,
    GeneratePPTRequest,
    GenerateProseRequest,
    TTSRequest,
)
from src.server.mcp_request import MCPServerMetadataRequest, MCPServerMetadataResponse
from src.server.mcp_utils import load_mcp_tools
from src.tools import VolcengineTTS

# Knowledge Base imports
from src.knowledge_base.graph.builder import build_kb_graph
from src.knowledge_base.graph.state import KnowledgeBaseState

logger = logging.getLogger(__name__)

app = FastAPI(
    title="DeerFlow API",
    description="API for Deer with Knowledge Base Building",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

graph = build_graph_with_memory()
kb_graph = build_kb_graph()


def detect_kb_intent(message: str) -> bool:
    """Detect if message is intended for knowledge base building"""
    kb_keywords = [
        "build knowledge base",
        "create knowledge base", 
        "kb building",
        "knowledge acquisition",
        "gather articles about",
        "collect knowledge on",
        "build kb on",
        "[kb_mode]"
    ]
    
    message_lower = message.lower()
    return any(keyword in message_lower for keyword in kb_keywords)


def extract_kb_parameters(message: str) -> dict:
    """Extract KB building parameters from message"""
    # Extract topics
    topics = []
    message_lower = message.lower()
    
    # Common topic keywords
    common_topics = [
        "artificial intelligence", "machine learning", "blockchain", 
        "cybersecurity", "cloud computing", "data science", "robotics",
        "finance", "healthcare", "technology", "marketing", "automation"
    ]
    
    for topic in common_topics:
        if topic in message_lower:
            topics.append(topic)
    
    # Extract topics after "about", "on", "regarding"
    if not topics:
        patterns = [
            r"(?:about|on|regarding|for)\s+([a-zA-Z\s]+?)(?:\s|$|\.|,)",
            r"knowledge base\s+(?:on|about|for)\s+([a-zA-Z\s]+?)(?:\s|$|\.|,)"
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, message, re.IGNORECASE)
            if matches:
                topics.extend([match.strip() for match in matches])
                break
    
    # Extract target article count
    target_articles = 20  # default
    count_patterns = [
        r"(\d+)\s+articles?",
        r"gather\s+(\d+)",
        r"collect\s+(\d+)",
        r"find\s+(\d+)"
    ]
    
    for pattern in count_patterns:
        match = re.search(pattern, message, re.IGNORECASE)
        if match:
            count = int(match.group(1))
            target_articles = min(100, max(5, count))  # Limit between 5-100
            break
    
    return {
        "topics": topics[:3] if topics else ["general technology"],
        "target_article_count": target_articles,
        "industries": [],
        "locale": "en-US"
    }


@app.post("/api/chat/stream")
async def chat_stream(request: ChatRequest):
    thread_id = request.thread_id
    if thread_id == "__default__":
        thread_id = str(uuid4())
    
    # Check if last message is KB building intent
    if request.messages and detect_kb_intent(request.messages[-1].content):
        # Route to KB building stream
        return StreamingResponse(
            _astream_kb_workflow_generator(
                request.messages,
                thread_id,
                request.max_step_num,
                request.max_search_results,
            ),
            media_type="text/event-stream",
        )
    else:
        # Route to normal research stream
        return StreamingResponse(
            _astream_workflow_generator(
                request.model_dump()["messages"],
                thread_id,
                request.max_plan_iterations,
                request.max_step_num,
                request.max_search_results,
                request.auto_accepted_plan,
                request.interrupt_feedback,
                request.mcp_settings,
                request.enable_background_investigation,
            ),
            media_type="text/event-stream",
        )


async def _astream_kb_workflow_generator(
    messages: List[ChatMessage],
    thread_id: str,
    max_step_num: int,
    max_search_results: int,
):
    """Stream KB building workflow using standard research events"""
    
    # Extract KB parameters from the last message
    last_message = messages[-1].content if messages else ""
    kb_params = extract_kb_parameters(last_message)
    
    # Create initial KB state
    initial_state = KnowledgeBaseState(
        messages=[],
        target_topics=kb_params["topics"],
        target_article_count=kb_params["target_article_count"],
        target_industries=kb_params["industries"],
        locale=kb_params["locale"],
        current_plan=None,
        plan_iterations=0,
        url_candidates={},
        pending_urls=set(),
        processing_urls=set(),
        processed_urls=set(),
        failed_urls=set(),
        article_counts_by_topic={topic: 0 for topic in kb_params["topics"]},
        article_counts_by_industry={},
        processed_article_count=0,
        processed_chunks=[],
        last_error=None
    )
    
    # Send initial user message
    yield _make_event("message_chunk", {
        "thread_id": thread_id,
        "agent": "user",
        "id": str(uuid4()),
        "role": "user",
        "content": last_message,
    })
    
    # Stream KB workflow execution
    config = {
        "configurable": {
            "max_step_num": max_step_num,
            "max_search_results": max_search_results
        }
    }
    
    try:
        async for event in kb_graph.astream(
            input=initial_state,
            config=config,
            stream_mode=["messages", "updates"]
        ):
            if isinstance(event, dict):
                # Handle updates (node completion)
                for node_name, node_output in event.items():
                    if node_name != "__end__":
                        # Extract and send any messages from the node
                        if hasattr(node_output, 'get') and 'messages' in node_output:
                            for message in node_output['messages']:
                                yield _convert_kb_message_to_event(message, thread_id)
                        
                        # Send progress update if this is a significant node
                        if node_name in ["kb_injector"]:
                            progress_data = _extract_progress_from_state(node_output)
                            if progress_data:
                                yield _make_kb_progress_event(progress_data, thread_id)
            else:
                # Handle message events directly
                message_chunk, message_metadata = cast(
                    tuple[BaseMessage, dict[str, any]], event
                )
                yield _convert_kb_message_to_event(message_chunk, thread_id)
                
    except Exception as e:
        logger.error(f"Error in KB workflow: {e}")
        yield _make_event("message_chunk", {
            "thread_id": thread_id,
            "agent": "kb_coordinator",
            "id": str(uuid4()),
            "role": "assistant", 
            "content": f"Knowledge base building encountered an error: {str(e)}",
            "finish_reason": "stop"
        })


def _convert_kb_message_to_event(message: BaseMessage, thread_id: str):
    """Convert KB workflow messages to standard chat events"""
    
    if hasattr(message, 'tool_calls') and message.tool_calls:
        # Handle tool calls
        return _make_event("tool_calls", {
            "thread_id": thread_id,
            "agent": getattr(message, 'name', 'kb_agent'),
            "id": getattr(message, 'id', str(uuid4())),
            "role": "assistant",
            "tool_calls": message.tool_calls,
            "tool_call_chunks": []
        })
    elif isinstance(message, ToolMessage):
        # Handle tool results - check if it contains progress data
        content = message.content
        try:
            # Try to parse as JSON for progress updates
            parsed_content = json.loads(content)
            if isinstance(parsed_content, dict) and "progress" in parsed_content:
                # This is a progress update from kb_database_store
                return _make_kb_progress_event(parsed_content["progress"], thread_id, message.tool_call_id)
            else:
                # Regular tool result
                return _make_event("tool_call_result", {
                    "thread_id": thread_id,
                    "id": getattr(message, 'id', str(uuid4())),
                    "role": "tool",
                    "tool_call_id": message.tool_call_id,
                    "content": parsed_content.get("message", content) if isinstance(parsed_content, dict) else content
                })
        except (json.JSONDecodeError, AttributeError):
            # Regular tool result
            return _make_event("tool_call_result", {
                "thread_id": thread_id,
                "id": getattr(message, 'id', str(uuid4())),
                "role": "tool",
                "tool_call_id": message.tool_call_id,
                "content": content
            })
    else:
        # Handle regular messages
        return _make_event("message_chunk", {
            "thread_id": thread_id,
            "agent": getattr(message, 'name', 'kb_agent'),
            "id": getattr(message, 'id', str(uuid4())),
            "role": "assistant",
            "content": message.content,
        })


def _make_kb_progress_event(progress_data: dict, thread_id: str, tool_call_id: str = None):
    """Create a special tool result event for KB progress updates"""
    return _make_event("tool_call_result", {
        "thread_id": thread_id,
        "id": str(uuid4()),
        "role": "tool",
        "tool_call_id": tool_call_id or "kb_progress",
        "content": json.dumps({
            "type": "kb_progress",
            "data": progress_data
        })
    })


def _extract_progress_from_state(state_output: dict) -> dict:
    """Extract progress information from KB state"""
    if not isinstance(state_output, dict):
        return None
    
    processed_count = state_output.get("processed_article_count", 0)
    target_count = state_output.get("target_article_count", 1)
    
    return {
        "stored": processed_count,
        "target": target_count,
        "percentage": (processed_count / target_count) * 100,
        "by_topic": state_output.get("article_counts_by_topic", {}),
        "by_industry": state_output.get("article_counts_by_industry", {}),
        "pending": len(state_output.get("pending_urls", [])),
        "processing": len(state_output.get("processing_urls", [])),
        "failed": len(state_output.get("failed_urls", []))
    }


async def _astream_workflow_generator(
    messages: List[ChatMessage],
    thread_id: str,
    max_plan_iterations: int,
    max_step_num: int,
    max_search_results: int,
    auto_accepted_plan: bool,
    interrupt_feedback: str,
    mcp_settings: dict,
    enable_background_investigation,
):
    """Original research workflow - unchanged"""
    input_ = {
        "messages": messages,
        "plan_iterations": 0,
        "final_report": "",
        "current_plan": None,
        "observations": [],
        "auto_accepted_plan": auto_accepted_plan,
        "enable_background_investigation": enable_background_investigation,
    }
    if not auto_accepted_plan and interrupt_feedback:
        resume_msg = f"[{interrupt_feedback}]"
        # add the last message to the resume message
        if messages:
            resume_msg += f" {messages[-1].content}"
        input_ = Command(resume=resume_msg)
    async for agent, _, event_data in graph.astream(
        input_,
        config={
            "thread_id": thread_id,
            "max_plan_iterations": max_plan_iterations,
            "max_step_num": max_step_num,
            "max_search_results": max_search_results,
            "mcp_settings": mcp_settings,
        },
        stream_mode=["messages", "updates"],
        subgraphs=True,
    ):
        if isinstance(event_data, dict):
            if "__interrupt__" in event_data:
                yield _make_event(
                    "interrupt",
                    {
                        "thread_id": thread_id,
                        "id": event_data["__interrupt__"][0].ns[0],
                        "role": "assistant",
                        "content": event_data["__interrupt__"][0].value,
                        "finish_reason": "interrupt",
                        "options": [
                            {"text": "Edit plan", "value": "edit_plan"},
                            {"text": "Start research", "value": "accepted"},
                        ],
                    },
                )
            continue
        message_chunk, message_metadata = cast(
            tuple[BaseMessage, dict[str, any]], event_data
        )
        event_stream_message: dict[str, any] = {
            "thread_id": thread_id,
            "agent": agent[0].split(":")[0],
            "id": message_chunk.id,
            "role": "assistant",
            "content": message_chunk.content,
        }
        if message_chunk.response_metadata.get("finish_reason"):
            event_stream_message["finish_reason"] = message_chunk.response_metadata.get(
                "finish_reason"
            )
        if isinstance(message_chunk, ToolMessage):
            # Tool Message - Return the result of the tool call
            event_stream_message["tool_call_id"] = message_chunk.tool_call_id
            yield _make_event("tool_call_result", event_stream_message)
        elif isinstance(message_chunk, AIMessageChunk):
            # AI Message - Raw message tokens
            if message_chunk.tool_calls:
                # AI Message - Tool Call
                event_stream_message["tool_calls"] = message_chunk.tool_calls
                event_stream_message["tool_call_chunks"] = (
                    message_chunk.tool_call_chunks
                )
                yield _make_event("tool_calls", event_stream_message)
            elif message_chunk.tool_call_chunks:
                # AI Message - Tool Call Chunks
                event_stream_message["tool_call_chunks"] = (
                    message_chunk.tool_call_chunks
                )
                yield _make_event("tool_call_chunks", event_stream_message)
            else:
                # AI Message - Raw message tokens
                yield _make_event("message_chunk", event_stream_message)


def _make_event(event_type: str, data: dict[str, any]):
    if data.get("content") == "":
        data.pop("content")
    return f"event: {event_type}\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"


# === Rest of the existing endpoints unchanged ===

@app.post("/api/tts")
async def text_to_speech(request: TTSRequest):
    """Convert text to speech using volcengine TTS API."""
    try:
        app_id = os.getenv("VOLCENGINE_TTS_APPID", "")
        if not app_id:
            raise HTTPException(
                status_code=400, detail="VOLCENGINE_TTS_APPID is not set"
            )
        access_token = os.getenv("VOLCENGINE_TTS_ACCESS_TOKEN", "")
        if not access_token:
            raise HTTPException(
                status_code=400, detail="VOLCENGINE_TTS_ACCESS_TOKEN is not set"
            )
        cluster = os.getenv("VOLCENGINE_TTS_CLUSTER", "volcano_tts")
        voice_type = os.getenv("VOLCENGINE_TTS_VOICE_TYPE", "BV700_V2_streaming")

        tts_client = VolcengineTTS(
            appid=app_id,
            access_token=access_token,
            cluster=cluster,
            voice_type=voice_type,
        )
        # Call the TTS API
        result = tts_client.text_to_speech(
            text=request.text[:1024],
            encoding=request.encoding,
            speed_ratio=request.speed_ratio,
            volume_ratio=request.volume_ratio,
            pitch_ratio=request.pitch_ratio,
            text_type=request.text_type,
            with_frontend=request.with_frontend,
            frontend_type=request.frontend_type,
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=str(result["error"]))

        # Decode the base64 audio data
        audio_data = base64.b64decode(result["audio_data"])

        # Return the audio file
        return Response(
            content=audio_data,
            media_type=f"audio/{request.encoding}",
            headers={
                "Content-Disposition": (
                    f"attachment; filename=tts_output.{request.encoding}"
                )
            },
        )
    except Exception as e:
        logger.exception(f"Error in TTS endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/podcast/generate")
async def generate_podcast(request: GeneratePodcastRequest):
    try:
        report_content = request.content
        print(report_content)
        workflow = build_podcast_graph()
        final_state = workflow.invoke({"input": report_content})
        audio_bytes = final_state["output"]
        return Response(content=audio_bytes, media_type="audio/mp3")
    except Exception as e:
        logger.exception(f"Error occurred during podcast generation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/ppt/generate")
async def generate_ppt(request: GeneratePPTRequest):
    try:
        report_content = request.content
        print(report_content)
        workflow = build_ppt_graph()
        final_state = workflow.invoke({"input": report_content})
        generated_file_path = final_state["generated_file_path"]
        with open(generated_file_path, "rb") as f:
            ppt_bytes = f.read()
        return Response(
            content=ppt_bytes,
            media_type="application/vnd.openxmlformats-officedocument.presentationml.presentation",
        )
    except Exception as e:
        logger.exception(f"Error occurred during ppt generation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/prose/generate")
async def generate_prose(request: GenerateProseRequest):
    try:
        logger.info(f"Generating prose for prompt: {request.prompt}")
        workflow = build_prose_graph()
        events = workflow.astream(
            {
                "content": request.prompt,
                "option": request.option,
                "command": request.command,
            },
            stream_mode="messages",
            subgraphs=True,
        )
        return StreamingResponse(
            (f"data: {event[0].content}\n\n" async for _, event in events),
            media_type="text/event-stream",
        )
    except Exception as e:
        logger.exception(f"Error occurred during prose generation: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/mcp/server/metadata", response_model=MCPServerMetadataResponse)
async def mcp_server_metadata(request: MCPServerMetadataRequest):
    """Get information about an MCP server."""
    try:
        # Set default timeout with a longer value for this endpoint
        timeout = 300  # Default to 300 seconds for this endpoint

        # Use custom timeout from request if provided
        if request.timeout_seconds is not None:
            timeout = request.timeout_seconds

        # Load tools from the MCP server using the utility function
        tools = await load_mcp_tools(
            server_type=request.transport,
            command=request.command,
            args=request.args,
            url=request.url,
            env=request.env,
            timeout_seconds=timeout,
        )

        # Create the response with tools
        response = MCPServerMetadataResponse(
            transport=request.transport,
            command=request.command,
            args=request.args,
            url=request.url,
            env=request.env,
            tools=tools,
        )

        return response
    except Exception as e:
        if not isinstance(e, HTTPException):
            logger.exception(f"Error in MCP server metadata endpoint: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
        raise e