# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
Knowledge Base Builder for DeerFlow

This module provides automated knowledge base building capabilities for DeerFlow.
It discovers, processes, and stores high-quality content from the web based on 
specified topics and constraints.

Example usage:
    import asyncio
    from src.knowledge_base import run_kb_builder
    
    async def build_kb():
        result = await run_kb_builder(
            topics=["artificial intelligence", "machine learning"],
            target_article_count=20,
            industries=["technology", "healthcare"],
            locale="en-US"
        )
        return result
    
    # Run the builder
    result = asyncio.run(build_kb())
"""

from .graph.builder import build_kb_graph, run_kb_builder, kb_graph
from .graph.state import KnowledgeBaseState, URLStatus
from .graph.nodes import get_kb_status
from .database import KnowledgeBaseDB, kb_db

__version__ = "1.0.0"
__author__ = "DeerFlow Team"

__all__ = [
    # Main functions
    "run_kb_builder",
    "build_kb_graph", 
    "kb_graph",
    
    # State management
    "KnowledgeBaseState",
    "URLStatus",
    
    # Database
    "KnowledgeBaseDB", 
    "kb_db",
    
    # Utilities
    "get_kb_status"
]