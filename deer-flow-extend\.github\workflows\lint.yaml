name: Lint Check

on:
  push:
    branches: [ 'main' ]
  pull_request:
    branches: [ '*' ]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Install the latest version of uv
      uses: astral-sh/setup-uv@v5
      with:
        version: "latest"

    - name: Install dependencies
      run: |
        uv venv --python 3.12
        uv pip install -e ".[dev]"

    - name: Run linters
      run: |
        source .venv/bin/activate
        make lint