# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import re
from datetime import datetime
from typing import Dict, List, Literal, Optional, Any

from langchain_core.messages import AIMessage, HumanMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command

from src.config.agents import AGENT_LLM_MAP
from src.config.configuration import Configuration
from src.llms.llm import get_llm_by_type
from src.prompts.template import apply_prompt_template
from src.prompts.planner_model import Plan
from src.tools import crawl_tool, get_web_search_tool
from src.utils.json_utils import repair_json_output

from .state import KnowledgeBaseState, URLStatus
from ..database import kb_db

logger = logging.getLogger(__name__)


async def kb_planner_node(
    state: KnowledgeBaseState, config: RunnableConfig
) -> Command[Literal["kb_research_team", "__end__"]]:
    """
    Knowledge Base Planner - now emits standard research events
    """
    logger.info("KB Planner generating research plan")
    configurable = Configuration.from_runnable_config(config)
    
    # Check if we've reached target article count
    if state["processed_article_count"] >= state["target_article_count"]:
        logger.info(f"Target article count ({state['target_article_count']}) reached")
        
        # Send completion message using standard event
        completion_message = AIMessage(
            content=f"Knowledge base building completed! Successfully acquired {state['processed_article_count']} articles across {len(state['target_topics'])} topics.",
            name="kb_planner"
        )
        
        return Command(
            update={"messages": state["messages"] + [completion_message]},
            goto="__end__"
        )
    
    # Send planning message using standard event
    remaining_articles = state["target_article_count"] - state["processed_article_count"]
    planning_message = AIMessage(
        content=f"Planning knowledge acquisition for: {', '.join(state['target_topics'])}\nTarget: {state['target_article_count']} articles | Progress: {state['processed_article_count']} collected | Remaining: {remaining_articles}",
        name="kb_planner"
    )
    
    # Prepare planning context
    planning_context = {
        "messages": [
            HumanMessage(content=f"""
            Plan knowledge acquisition for the following requirements:
            
            Target Topics: {', '.join(state['target_topics'])}
            Target Industries: {', '.join(state['target_industries']) if state['target_industries'] else 'General'}
            Target Article Count: {state['target_article_count']}
            Current Progress: {state['processed_article_count']} articles processed
            Remaining: {remaining_articles} articles needed
            
            Current distribution by topic: {state['article_counts_by_topic']}
            Current distribution by industry: {state['article_counts_by_industry']}
            
            Create a research plan to acquire high-quality articles covering these topics.
            Focus on areas that are currently underrepresented.
            """)
        ],
        "locale": state["locale"],
        "target_topics": state["target_topics"],
        "target_industries": state["target_industries"],
        "target_article_count": state["target_article_count"],
        "processed_url_count": state["processed_article_count"],
        "article_counts": state["article_counts_by_topic"],
        "max_step_num": getattr(configurable, "max_step_num", 5)
    }
    
    # Apply prompt template
    messages = apply_prompt_template("knowledge_base/kb_planner", planning_context, configurable)
    
    # Get LLM and generate plan
    llm = get_llm_by_type(AGENT_LLM_MAP["planner"]).with_structured_output(
        Plan, method="json_mode"
    )
    
    try:
        plan = llm.invoke(messages)
        
        if plan.has_enough_context or not plan.steps:
            logger.info("Planner determined sufficient context or no more steps needed")
            
            final_message = AIMessage(
                content=f"Knowledge acquisition planning complete. Total articles acquired: {state['processed_article_count']}",
                name="kb_planner"
            )
            
            return Command(
                update={
                    "current_plan": plan,
                    "plan_iterations": state["plan_iterations"] + 1,
                    "messages": state["messages"] + [planning_message, final_message]
                },
                goto="__end__"
            )
        
        logger.info(f"Generated research plan with {len(plan.steps)} steps: {plan.title}")
        
        plan_details_message = AIMessage(
            content=f"Created research plan: {plan.title}\nSteps: {len(plan.steps)} research activities planned",
            name="kb_planner"
        )
        
        return Command(
            update={
                "current_plan": plan,
                "plan_iterations": state["plan_iterations"] + 1,
                "messages": state["messages"] + [planning_message, plan_details_message]
            },
            goto="kb_research_team"
        )
        
    except Exception as e:
        logger.error(f"Error in KB planner: {e}")
        error_message = AIMessage(
            content=f"Planning error occurred: {str(e)}",
            name="kb_planner"
        )
        
        return Command(
            update={
                "last_error": f"Planning error: {str(e)}",
                "messages": state["messages"] + [planning_message, error_message]
            },
            goto="__end__"
        )


async def kb_research_team_node(
    state: KnowledgeBaseState
) -> Command[Literal["kb_researcher", "kb_planner", "__end__"]]:
    """
    Research Team - supervises researchers and manages plan execution.
    """
    logger.info("Research Team coordinating knowledge acquisition")
    
    # Check if target reached
    if state["processed_article_count"] >= state["target_article_count"]:
        logger.info("Target article count reached, returning to planner for completion")
        return Command(goto="kb_planner")
    
    # Check if we have a valid plan
    current_plan = state.get("current_plan")
    if not current_plan or not current_plan.steps:
        logger.info("No valid plan available, returning to planner")
        return Command(goto="kb_planner")
    
    # Check if we have pending URLs to process
    if state["pending_urls"]:
        logger.info(f"Dispatching researcher to process {len(state['pending_urls'])} pending URLs")
        return Command(goto="kb_researcher")
    
    # Check if any steps still need execution
    for step in current_plan.steps:
        if not getattr(step, "execution_res", None):
            logger.info(f"Dispatching researcher for step: {step.title}")
            return Command(goto="kb_researcher")
    
    # All steps completed, return to planner
    logger.info("All plan steps completed, returning to planner")
    return Command(goto="kb_planner")


async def kb_researcher_node(
    state: KnowledgeBaseState, config: RunnableConfig
) -> Command[Literal["kb_processor", "kb_research_team"]]:
    """
    Researcher - now using standard tool_calls and tool_call_result events
    """
    logger.info("KB Researcher finding and evaluating content sources")
    configurable = Configuration.from_runnable_config(config)
    
    # If we have pending URLs, process one
    if state["pending_urls"]:
        url = next(iter(state["pending_urls"]))
        url_data = state["url_candidates"][url]
        
        # Update URL status to processing
        updated_url_candidates = state["url_candidates"].copy()
        updated_url_candidates[url]["status"] = URLStatus.PROCESSING
        
        updated_pending = state["pending_urls"].copy()
        updated_pending.remove(url)
        
        updated_processing = state["processing_urls"].copy()
        updated_processing.add(url)
        
        logger.info(f"Processing pending URL: {url}")
        
        return Command(
            update={
                "url_candidates": updated_url_candidates,
                "pending_urls": updated_pending,
                "processing_urls": updated_processing,
                "current_url": url,
                "current_topic": url_data.get("topic"),
                "current_industry": url_data.get("industry")
            },
            goto="kb_processor"
        )
    
    # Find next unexecuted step
    current_plan = state.get("current_plan")
    if not current_plan or not current_plan.steps:
        return Command(goto="kb_research_team")
    
    unexecuted_step = None
    for step in current_plan.steps:
        if not getattr(step, "execution_res", None):
            unexecuted_step = step
            break
    
    if not unexecuted_step:
        return Command(goto="kb_research_team")
    
    # Execute search for this step - using standard tool call events
    search_query = unexecuted_step.description
    logger.info(f"Executing search for step '{unexecuted_step.title}': {search_query[:100]}...")
    
    try:
        # Create standard tool call message
        search_message = AIMessage(
            content=f"Searching for knowledge sources: {unexecuted_step.title}",
            name="kb_researcher",
            tool_calls=[{
                "name": "web_search",
                "args": {"query": search_query},
                "id": f"search_{len(state.get('messages', []))}"
            }]
        )
        
        # Perform web search
        search_tool = get_web_search_tool(getattr(configurable, "max_search_results", 5))
        search_results = search_tool.invoke(search_query)
        
        # Create tool result message
        search_result_message = ToolMessage(
            content=f"Found {len(search_results) if isinstance(search_results, list) else 0} potential knowledge sources",
            tool_call_id=search_message.tool_calls[0]["id"]
        )
        
        # Extract URLs from search results
        new_urls = {}
        if isinstance(search_results, list):
            for result in search_results:
                if isinstance(result, dict) and "url" in result:
                    url = result["url"]
                    if url not in state["url_candidates"]:
                        new_urls[url] = {
                            "title": result.get("title", ""),
                            "snippet": result.get("content", result.get("snippet", ""))
                        }
        
        if not new_urls:
            # Mark step as executed with no results
            unexecuted_step.execution_res = "No relevant URLs found"
            logger.warning(f"No new URLs found for step: {unexecuted_step.title}")
            
            no_results_message = AIMessage(
                content=f"No new knowledge sources found for: {unexecuted_step.title}",
                name="kb_researcher"
            )
            
            return Command(
                update={
                    "current_plan": current_plan,
                    "messages": state["messages"] + [search_message, search_result_message, no_results_message]
                },
                goto="kb_research_team"
            )
        
        # Evaluate URLs for relevance using standard tool call
        eval_message = AIMessage(
            content=f"Evaluating {len(new_urls)} knowledge sources for relevance",
            name="kb_researcher",
            tool_calls=[{
                "name": "kb_url_evaluator",
                "args": {
                    "urls": list(new_urls.keys()),
                    "step_title": unexecuted_step.title,
                    "topics": state["target_topics"]
                },
                "id": f"eval_{len(state.get('messages', []))}"
            }]
        )
        
        # Process evaluation (simplified for demo)
        approved_urls = []
        updated_url_candidates = state["url_candidates"].copy()
        
        # Simple approval logic - can be enhanced with AI evaluation
        for url, data in new_urls.items():
            # Determine topic and industry for this URL
            topic = unexecuted_step.title
            industry = state["target_industries"][0] if state["target_industries"] else "general"
            
            updated_url_candidates[url] = {
                "status": URLStatus.PENDING,
                "topic": topic,
                "industry": industry,
                "title": data["title"],
                "snippet": data["snippet"],
                "relevance_score": 7,  # Default score
                "quality": "medium",
                "step_title": unexecuted_step.title,
                "timestamp": datetime.now().isoformat()
            }
            approved_urls.append(url)
        
        # Update pending URLs
        updated_pending = state["pending_urls"].copy()
        updated_pending.update(approved_urls)
        
        # Mark step as executed
        unexecuted_step.execution_res = f"Found {len(approved_urls)} relevant URLs"
        
        # Create evaluation result message
        eval_result_message = ToolMessage(
            content=f"Approved {len(approved_urls)} knowledge sources for processing",
            tool_call_id=eval_message.tool_calls[0]["id"]
        )
        
        logger.info(f"Step '{unexecuted_step.title}' completed: {len(approved_urls)} URLs added")
        
        return Command(
            update={
                "url_candidates": updated_url_candidates,
                "pending_urls": updated_pending,
                "current_plan": current_plan,
                "messages": state["messages"] + [search_message, search_result_message, eval_message, eval_result_message]
            },
            goto="kb_research_team"
        )
        
    except Exception as e:
        logger.error(f"Error in researcher for step '{unexecuted_step.title}': {e}")
        unexecuted_step.execution_res = f"Error: {str(e)}"
        
        error_message = AIMessage(
            content=f"Research error for step '{unexecuted_step.title}': {str(e)}",
            name="kb_researcher"
        )
        
        return Command(
            update={
                "current_plan": current_plan,
                "last_error": f"Research error: {str(e)}",
                "messages": state["messages"] + [error_message]
            },
            goto="kb_research_team"
        )


async def kb_processor_node(
    state: KnowledgeBaseState, config: RunnableConfig
) -> Command[Literal["kb_injector", "kb_research_team"]]:
    """
    Processor - now using standard tool_calls and tool_call_result events
    """
    logger.info(f"KB Processor handling URL: {state['current_url']}")
    
    current_url = state["current_url"]
    if not current_url:
        logger.warning("No current URL to process")
        return Command(goto="kb_research_team")
    
    try:
        # Create crawling tool call message
        crawl_message = AIMessage(
            content=f"Processing knowledge source: {state['url_candidates'][current_url].get('title', current_url)}",
            name="kb_processor",
            tool_calls=[{
                "name": "crawl_tool",
                "args": {"url": current_url},
                "id": f"crawl_{len(state.get('messages', []))}"
            }]
        )
        
        # Step 1: Fetch web content
        logger.info(f"Crawling content from: {current_url}")
        crawl_result = crawl_tool.invoke(current_url)
        
        if not isinstance(crawl_result, dict) or "crawled_content" not in crawl_result:
            raise ValueError(f"Failed to crawl content from {current_url}")
        
        raw_content = crawl_result["crawled_content"]
        
        if not raw_content or len(raw_content) < 200:
            raise ValueError(f"Content too short or empty for {current_url}")
        
        # Create crawl result message
        crawl_result_message = ToolMessage(
            content=f"Successfully crawled {len(raw_content)} characters of content",
            tool_call_id=crawl_message.tool_calls[0]["id"]
        )
        
        # Step 2: Process content (simplified)
        logger.info("Processing and chunking content")
        
        processing_message = AIMessage(
            content="Processing content: chunking and generating summaries",
            name="kb_processor", 
            tool_calls=[{
                "name": "kb_content_processor",
                "args": {
                    "url": current_url,
                    "content_length": len(raw_content),
                    "topic": state["current_topic"]
                },
                "id": f"process_{len(state.get('messages', []))}"
            }]
        )
        
        # Simple chunking strategy
        content_length = len(raw_content)
        if content_length < 1000:
            chunk_size = content_length
        elif content_length < 5000:
            chunk_size = 800
        else:
            chunk_size = 1200
        
        # Split into chunks
        chunks = []
        chunk_overlap = min(200, chunk_size // 4)
        
        for i in range(0, len(raw_content), chunk_size - chunk_overlap):
            chunk_text = raw_content[i:i + chunk_size]
            if len(chunk_text.strip()) > 50:  # Only keep substantial chunks
                chunks.append({
                    "chunk_index": len(chunks),
                    "chunk_text": chunk_text,
                    "token_count": len(chunk_text.split()),
                    "chunk_summary": f"Content chunk from {state['url_candidates'][current_url].get('title', 'article')}",
                    "key_concepts": [],
                    "embedding": [0.1] * 1024,  # Placeholder embedding
                    "timestamp": datetime.now().isoformat()
                })
        
        if not chunks:
            raise ValueError(f"No valid chunks generated for {current_url}")
        
        # Create processing result message
        processing_result_message = ToolMessage(
            content=f"Processed content into {len(chunks)} chunks with summaries",
            tool_call_id=processing_message.tool_calls[0]["id"]
        )
        
        logger.info(f"Successfully processed {len(chunks)} chunks for {current_url}")
        
        return Command(
            update={
                "raw_content": raw_content,
                "processed_chunks": chunks,
                "messages": state["messages"] + [crawl_message, crawl_result_message, processing_message, processing_result_message]
            },
            goto="kb_injector"
        )
        
    except Exception as e:
        logger.error(f"Error processing {current_url}: {e}")
        
        # Mark URL as failed
        updated_url_candidates = state["url_candidates"].copy()
        updated_url_candidates[current_url]["status"] = URLStatus.FAILED
        
        updated_processing = state["processing_urls"].copy()
        updated_processing.discard(current_url)
        
        updated_failed = state["failed_urls"].copy()
        updated_failed.add(current_url)
        
        error_message = AIMessage(
            content=f"Failed to process {current_url}: {str(e)}",
            name="kb_processor"
        )
        
        return Command(
            update={
                "url_candidates": updated_url_candidates,
                "processing_urls": updated_processing,
                "failed_urls": updated_failed,
                "current_url": None,
                "current_topic": None,
                "current_industry": None,
                "last_error": f"Processing error for {current_url}: {str(e)}",
                "messages": state["messages"] + [error_message]
            },
            goto="kb_research_team"
        )


async def kb_injector_node(
    state: KnowledgeBaseState, config: RunnableConfig
) -> Command[Literal["kb_research_team"]]:
    """
    Injector - now using standard tool_calls and tool_call_result events
    """
    logger.info("KB Injector storing processed content in database")
    
    current_url = state["current_url"]
    processed_chunks = state["processed_chunks"]
    
    if not current_url or not processed_chunks:
        logger.warning("No URL or processed chunks to inject")
        return Command(goto="kb_research_team")
    
    try:
        # Create database storage tool call message
        store_message = AIMessage(
            content=f"Storing knowledge article in database: {state['url_candidates'][current_url].get('title', 'Untitled')}",
            name="kb_injector",
            tool_calls=[{
                "name": "kb_database_store",
                "args": {
                    "url": current_url,
                    "title": state['url_candidates'][current_url].get('title', ''),
                    "topic": state["current_topic"],
                    "industry": state["current_industry"],
                    "chunks_count": len(processed_chunks)
                },
                "id": f"store_{len(state.get('messages', []))}"
            }]
        )
        
        # Get article metadata
        url_data = state["url_candidates"][current_url]
        article_title = url_data.get("title", "")
        article_summary = f"Article about {state['current_topic']} in {state['current_industry']}"
        industry = state["current_industry"]
        
        # Store in database
        success = await kb_db.store_article_with_chunks(
            url=current_url,
            title=article_title,
            summary=article_summary,
            industry=industry,
            chunks=processed_chunks
        )
        
        if not success:
            raise Exception("Database storage failed")
        
        # Update state - mark URL as processed and increment counters
        updated_url_candidates = state["url_candidates"].copy()
        updated_url_candidates[current_url]["status"] = URLStatus.PROCESSED
        
        updated_processing = state["processing_urls"].copy()
        updated_processing.discard(current_url)
        
        updated_processed = state["processed_urls"].copy()
        updated_processed.add(current_url)
        
        new_article_count = state["processed_article_count"] + 1
        
        updated_topic_counts = state["article_counts_by_topic"].copy()
        topic = state["current_topic"]
        updated_topic_counts[topic] = updated_topic_counts.get(topic, 0) + 1
        
        updated_industry_counts = state["article_counts_by_industry"].copy()
        updated_industry_counts[industry] = updated_industry_counts.get(industry, 0) + 1
        
        # Create storage result message with progress update
        progress_data = {
            "stored": new_article_count,
            "target": state["target_article_count"],
            "percentage": (new_article_count / state["target_article_count"]) * 100,
            "by_topic": updated_topic_counts,
            "by_industry": updated_industry_counts,
            "pending": len(state["pending_urls"]) - 1,  # -1 because we just processed one
            "processing": len(updated_processing),
            "failed": len(state["failed_urls"])
        }
        
        store_result_message = ToolMessage(
            content=json.dumps({
                "message": f"Successfully stored article: {article_title}",
                "progress": progress_data
            }),
            tool_call_id=store_message.tool_calls[0]["id"]
        )
        
        logger.info(f"Successfully stored article: {current_url} with {len(processed_chunks)} chunks")
        
        return Command(
            update={
                "url_candidates": updated_url_candidates,
                "processing_urls": updated_processing,
                "processed_urls": updated_processed,
                "processed_article_count": new_article_count,
                "article_counts_by_topic": updated_topic_counts,
                "article_counts_by_industry": updated_industry_counts,
                "current_url": None,
                "current_topic": None,
                "current_industry": None,
                "raw_content": None,
                "processed_chunks": [],
                "messages": state["messages"] + [store_message, store_result_message]
            },
            goto="kb_research_team"
        )
        
    except Exception as e:
        logger.error(f"Error injecting content for {current_url}: {e}")
        
        # Mark URL as failed
        updated_url_candidates = state["url_candidates"].copy()
        updated_url_candidates[current_url]["status"] = URLStatus.FAILED
        
        updated_processing = state["processing_urls"].copy()
        updated_processing.discard(current_url)
        
        updated_failed = state["failed_urls"].copy()
        updated_failed.add(current_url)
        
        error_message = AIMessage(
            content=f"Failed to store article in database: {str(e)}",
            name="kb_injector"
        )
        
        return Command(
            update={
                "url_candidates": updated_url_candidates,
                "processing_urls": updated_processing,
                "failed_urls": updated_failed,
                "current_url": None,
                "current_topic": None,
                "current_industry": None,
                "raw_content": None,
                "processed_chunks": [],
                "last_error": f"Injection error for {current_url}: {str(e)}",
                "messages": state["messages"] + [error_message]
            },
            goto="kb_research_team"
        )


def get_kb_status(state: KnowledgeBaseState) -> Dict[str, Any]:
    """Get current knowledge base building status."""
    return {
        "target_article_count": state["target_article_count"],
        "processed_article_count": state["processed_article_count"],
        "progress_percentage": (state["processed_article_count"] / state["target_article_count"]) * 100,
        "pending_urls": len(state["pending_urls"]),
        "processing_urls": len(state["processing_urls"]),
        "processed_urls": len(state["processed_urls"]),
        "failed_urls": len(state["failed_urls"]),
        "article_counts_by_topic": state["article_counts_by_topic"],
        "article_counts_by_industry": state["article_counts_by_industry"],
        "current_plan_title": state["current_plan"].title if state["current_plan"] else None,
        "plan_iterations": state["plan_iterations"],
        "last_error": state["last_error"]
    }