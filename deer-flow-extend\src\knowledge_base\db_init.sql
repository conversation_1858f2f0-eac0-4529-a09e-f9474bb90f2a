-- Enable pgvector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Create the schema
CREATE SCHEMA IF NOT EXISTS kb_main;

-- Define ENUM types for better data integrity and efficiency
CREATE TYPE kb_main.access_level_enum AS ENUM ('read', 'write', 'admin');
CREATE TYPE kb_main.source_type_enum AS ENUM ('web', 'pdf', 'document', 'api', 'user_upload', 'other');

-- Organization knowledge bases
CREATE TABLE IF NOT EXISTS kb_main.knowledge_base_org (
    id BIGSERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- User knowledge bases (related to organizations)
CREATE TABLE IF NOT EXISTS kb_main.knowledge_base_user (
    id BIGSERIAL PRIMARY KEY,
    org_id BIGINT NOT NULL REFERENCES kb_main.knowledge_base_org(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL,  -- Reference to application user
    access_level kb_main.access_level_enum NOT NULL DEFAULT 'read',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(org_id, user_id)
);

CREATE INDEX IF NOT EXISTS idx_kb_user_user_id ON kb_main.knowledge_base_user(user_id);

-- Crawled articles partitioned by industry
CREATE TABLE IF NOT EXISTS kb_main.kb_crawled_articles (
    id BIGSERIAL, -- Will be part of a composite PK
    url TEXT NOT NULL,
    title TEXT,
    summary TEXT,  -- AI-generated summary
    industry VARCHAR(100) NOT NULL, -- Partition key
    source_type kb_main.source_type_enum DEFAULT 'web',
    crawled_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, industry),       -- CORRECTED: PK includes partition key
    UNIQUE (url, industry)            -- CORRECTED: UNIQUE constraint includes partition key
) PARTITION BY LIST (industry);

-- Partitions for kb_crawled_articles (Ensure these cover your expected industries)
CREATE TABLE IF NOT EXISTS kb_main.kb_crawled_articles_technology PARTITION OF kb_main.kb_crawled_articles
    FOR VALUES IN ('technology');
CREATE TABLE IF NOT EXISTS kb_main.kb_crawled_articles_finance PARTITION OF kb_main.kb_crawled_articles
    FOR VALUES IN ('finance');
CREATE TABLE IF NOT EXISTS kb_main.kb_crawled_articles_healthcare PARTITION OF kb_main.kb_crawled_articles
    FOR VALUES IN ('healthcare');
CREATE TABLE IF NOT EXISTS kb_main.kb_crawled_articles_other PARTITION OF kb_main.kb_crawled_articles
    DEFAULT;

-- Chunks of crawled articles, PARTITIONED by industry
CREATE TABLE IF NOT EXISTS kb_main.kb_crawled_article_chunks (
    id BIGSERIAL, -- Will be part of a composite PK
    article_id BIGINT NOT NULL,
    chunk_text TEXT NOT NULL,
    chunk_index INTEGER NOT NULL,
    chunk_summary TEXT,
    token_count INTEGER NOT NULL,
    embedding vector(1024),
    industry VARCHAR(100) NOT NULL, -- Partition key, populated from parent article
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id, industry) -- Partition key must be part of the primary key.
) PARTITION BY LIST (industry);

-- Partitions for kb_crawled_article_chunks and their HNSW indexes & FKs
-- TECHNOLOGY Partition
CREATE TABLE IF NOT EXISTS kb_main.kb_crawled_article_chunks_technology
    PARTITION OF kb_main.kb_crawled_article_chunks
    FOR VALUES IN ('technology');
CREATE INDEX IF NOT EXISTS idx_crawled_chunks_tech_article_id
    ON kb_main.kb_crawled_article_chunks_technology(article_id);
CREATE INDEX IF NOT EXISTS kb_crawled_chunks_tech_embedding_idx
    ON kb_main.kb_crawled_article_chunks_technology USING hnsw (embedding vector_cosine_ops);
ALTER TABLE kb_main.kb_crawled_article_chunks_technology
    ADD CONSTRAINT fk_tech_chunks_to_articles FOREIGN KEY (article_id, industry) -- CORRECTED FK
    REFERENCES kb_main.kb_crawled_articles(id, industry) ON DELETE CASCADE;
ALTER TABLE kb_main.kb_crawled_article_chunks_technology
    ADD CONSTRAINT uq_chunk_tech UNIQUE (article_id, chunk_index);

-- FINANCE Partition
CREATE TABLE IF NOT EXISTS kb_main.kb_crawled_article_chunks_finance
    PARTITION OF kb_main.kb_crawled_article_chunks
    FOR VALUES IN ('finance');
CREATE INDEX IF NOT EXISTS idx_crawled_chunks_fin_article_id
    ON kb_main.kb_crawled_article_chunks_finance(article_id);
CREATE INDEX IF NOT EXISTS kb_crawled_chunks_fin_embedding_idx
    ON kb_main.kb_crawled_article_chunks_finance USING hnsw (embedding vector_cosine_ops);
ALTER TABLE kb_main.kb_crawled_article_chunks_finance
    ADD CONSTRAINT fk_fin_chunks_to_articles FOREIGN KEY (article_id, industry) -- CORRECTED FK
    REFERENCES kb_main.kb_crawled_articles(id, industry) ON DELETE CASCADE;
ALTER TABLE kb_main.kb_crawled_article_chunks_finance
    ADD CONSTRAINT uq_chunk_fin UNIQUE (article_id, chunk_index);

-- HEALTHCARE Partition
CREATE TABLE IF NOT EXISTS kb_main.kb_crawled_article_chunks_healthcare
    PARTITION OF kb_main.kb_crawled_article_chunks
    FOR VALUES IN ('healthcare');
CREATE INDEX IF NOT EXISTS idx_crawled_chunks_health_article_id
    ON kb_main.kb_crawled_article_chunks_healthcare(article_id);
CREATE INDEX IF NOT EXISTS kb_crawled_chunks_health_embedding_idx
    ON kb_main.kb_crawled_article_chunks_healthcare USING hnsw (embedding vector_cosine_ops);
ALTER TABLE kb_main.kb_crawled_article_chunks_healthcare
    ADD CONSTRAINT fk_health_chunks_to_articles FOREIGN KEY (article_id, industry) -- CORRECTED FK
    REFERENCES kb_main.kb_crawled_articles(id, industry) ON DELETE CASCADE;
ALTER TABLE kb_main.kb_crawled_article_chunks_healthcare
    ADD CONSTRAINT uq_chunk_health UNIQUE (article_id, chunk_index);

-- OTHER (DEFAULT) Partition
CREATE TABLE IF NOT EXISTS kb_main.kb_crawled_article_chunks_other
    PARTITION OF kb_main.kb_crawled_article_chunks
    DEFAULT;
CREATE INDEX IF NOT EXISTS idx_crawled_chunks_other_article_id
    ON kb_main.kb_crawled_article_chunks_other(article_id);
CREATE INDEX IF NOT EXISTS kb_crawled_chunks_other_embedding_idx
    ON kb_main.kb_crawled_article_chunks_other USING hnsw (embedding vector_cosine_ops);
ALTER TABLE kb_main.kb_crawled_article_chunks_other
    ADD CONSTRAINT fk_other_chunks_to_articles FOREIGN KEY (article_id, industry) -- CORRECTED FK
    REFERENCES kb_main.kb_crawled_articles(id, industry) ON DELETE CASCADE;
ALTER TABLE kb_main.kb_crawled_article_chunks_other
    ADD CONSTRAINT uq_chunk_other UNIQUE (article_id, chunk_index);

-- User-added articles (remains unpartitioned by industry)
CREATE TABLE IF NOT EXISTS kb_main.kb_user_added_articles (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    kb_id BIGINT REFERENCES kb_main.knowledge_base_user(id) ON DELETE CASCADE,
    title TEXT,
    summary TEXT,
    source_type kb_main.source_type_enum DEFAULT 'user_upload',
    is_private BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_user_added_articles_user_id ON kb_main.kb_user_added_articles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_added_articles_kb_id ON kb_main.kb_user_added_articles(kb_id);

-- Chunks of user-added articles (remains unpartitioned, global HNSW index)
CREATE TABLE IF NOT EXISTS kb_main.kb_user_added_chunks (
    id BIGSERIAL PRIMARY KEY,
    article_id BIGINT NOT NULL REFERENCES kb_main.kb_user_added_articles(id) ON DELETE CASCADE,
    chunk_text TEXT NOT NULL,
    chunk_index INTEGER NOT NULL,
    chunk_summary TEXT,
    token_count INTEGER NOT NULL,
    embedding vector(1024),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT unique_user_chunk_in_article UNIQUE (article_id, chunk_index)
);
CREATE INDEX IF NOT EXISTS idx_user_added_chunks_article_id ON kb_main.kb_user_added_chunks(article_id);
CREATE INDEX IF NOT EXISTS kb_user_chunks_embedding_idx ON kb_main.kb_user_added_chunks
    USING hnsw (embedding vector_cosine_ops);