# Knowledge Base Builder for DeerFlow

This module extends DeerFlow with knowledge base building capabilities. It automatically discovers, processes, and stores knowledge from the web based on specified topics and constraints.

## Overview

The Knowledge Base Builder uses a graph-based workflow to:

1. **Plan knowledge acquisition** based on user-defined topics and constraints
2. **Research and discover** relevant content sources
3. **Process content** through crawling, chunking, and summarization
4. **Inject processed knowledge** into a PostgreSQL database with pgvector for vector search

## Architecture

The system follows the same graph architecture as other DeerFlow components:

- **kb_planner_node**: Plans knowledge acquisition strategy based on topics
- **kb_researcher_node**: Discovers relevant content sources through search
- **kb_processor_node**: Processes content through crawling, chunking, and summarization
- **kb_injection_node**: Stores processed knowledge in the database

## Setup

### Database Setup

The Knowledge Base Builder requires a PostgreSQL database with the pgvector extension. Run the DB initialization script:

```bash
psql -U postgres -d deerflow -f db_init.sql
```

### Environment Variables

Set the following environment variables:

```
# Database settings
KB_DB_NAME=deerflow
KB_DB_USER=postgres
KB_DB_PASSWORD=your_password
KB_DB_HOST=localhost
KB_DB_PORT=5432

# Search API settings (for content discovery)
SEARCH_API=tavily
TAVILY_API_KEY=your_key_here
```

## Usage

### API Usage

The Knowledge Base Builder is available through the DeerFlow API:

```
POST /api/kb/build
{
  "topics": ["artificial intelligence", "machine learning"],
  "target_article_count": 10,
  "industries": ["technology"],
  "locale": "en-US"
}
```

### Programmatic Usage

```python
from src.knowledge_base import run_kb_builder

# Run the KB builder
result = await run_kb_builder(
    topics=["artificial intelligence", "machine learning"],
    target_article_count=10,
    industries=["technology"],
    locale="en-US",
)

# Access results
final_status = result["final_status"]
processed_count = final_status["processed_url_count"]
article_counts = final_status["article_counts_by_topic"]
```

## Knowledge Base DB Schema

The system uses a PostgreSQL database with pgvector for storing knowledge:

- `kb_crawled_articles`: Stores metadata about crawled articles
- `kb_crawled_article_chunks`: Stores article chunks with vector embeddings

## Extending the System

### Adding New Knowledge Sources

To add new knowledge sources, extend the `kb_researcher_node` with additional discovery methods:

```python
# Example: Add support for academic paper sources
def kb_researcher_node(state, config):
    # ... existing code ...
    
    # Add academic paper search
    if current_topic in ["research", "academic"]:
        # Use academic paper search API
        paper_results = academic_search_tool.invoke(search_query)
        # Process results
        # ...
    
    # ... rest of the function ...
```

### Custom Content Processing

Customize content processing in the `kb_processor_node`:

```python
def kb_processor_node(state, config):
    # ... existing code ...
    
    # Add custom processing for specific content types
    if "pdf" in url:
        # PDF-specific processing
        # ...
    elif "github" in url:
        # GitHub repository processing
        # ...
    
    # ... rest of the function ...
```

## Best Practices

1. **Topic Specificity**: Define focused topics for better content relevance
2. **Industry Filtering**: Use industry filters to get domain-specific knowledge
3. **Content Quality**: Set higher quality thresholds for more reliable knowledge
4. **Database Organization**: Use consistent industry categorization for better querying
5. **Regular Updates**: Periodically refresh knowledge to keep it current