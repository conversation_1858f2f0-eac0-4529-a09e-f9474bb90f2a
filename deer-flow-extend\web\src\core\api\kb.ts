// web/src/core/api/kb.ts
// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { fetchStream } from "../sse";
import { resolveServiceURL } from "./resolve-service-url";

export interface BuildKnowledgeBaseRequest {
  topics: string[];
  target_article_count: number;
  industries?: string[];
  locale: string;
  max_step_num?: number;
  max_search_results?: number;
}

export interface KnowledgeBaseStatus {
  target_article_count: number;
  processed_article_count: number;
  progress_percentage: number;
  pending_urls: number;
  processing_urls: number;
  processed_urls: number;
  failed_urls: number;
  article_counts_by_topic: Record<string, number>;
  article_counts_by_industry: Record<string, number>;
  current_plan_title?: string;
  plan_iterations: number;
  last_error?: string;
}

export interface BuildKnowledgeBaseResponse {
  success: boolean;
  message: string;
  final_status?: KnowledgeBaseStatus;
  error?: string;
  execution_time_seconds?: number;
}

export interface KnowledgeBaseStreamEvent {
  event_type: "status_update" | "error" | "complete";
  timestamp: string;
  node?: string;
  status?: KnowledgeBaseStatus;
  message?: string;
  error?: string;
}

export async function buildKnowledgeBase(
  request: BuildKnowledgeBaseRequest
): Promise<BuildKnowledgeBaseResponse> {
  const response = await fetch(resolveServiceURL("kb/build"), {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(request),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
}

export async function* buildKnowledgeBaseStream(
  request: BuildKnowledgeBaseRequest,
  options: { abortSignal?: AbortSignal } = {}
): AsyncIterable<KnowledgeBaseStreamEvent> {
  const stream = fetchStream(resolveServiceURL("kb/build/stream"), {
    body: JSON.stringify(request),
    signal: options.abortSignal,
  });

  for await (const event of stream) {
    if (event.event === "kb_update" || event.event === "kb_error" || event.event === "kb_complete") {
      yield JSON.parse(event.data) as KnowledgeBaseStreamEvent;
    }
  }
}