// web/src/app/chat/components/kb-message-card.tsx
// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

"use client";

import { LoadingOutlined } from "@ant-design/icons";
import { motion } from "framer-motion";
import { 
  Database, 
  CheckCircle2, 
  XCircle, 
  Clock, 
  FileText, 
  Globe,
  TrendingUp,
  AlertCircle
} from "lucide-react";
import { useMemo } from "react";

import { RainbowText } from "~/components/deer-flow/rainbow-text";
import { Progress } from "~/components/ui/progress";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { cn } from "~/lib/utils";
import type { Message } from "~/core/messages";

interface KnowledgeBaseData {
  type: "kb_building";
  request: {
    topics: string[];
    target_article_count: number;
    industries?: string[];
    locale: string;
  };
  buildId: string;
  status: "starting" | "building" | "complete" | "error" | "cancelled";
  currentStatus?: {
    target_article_count: number;
    processed_article_count: number;
    progress_percentage: number;
    pending_urls: number;
    processing_urls: number;
    processed_urls: number;
    failed_urls: number;
    article_counts_by_topic: Record<string, number>;
    article_counts_by_industry: Record<string, number>;
    current_plan_title?: string;
    plan_iterations: number;
    last_error?: string;
  };
  finalStatus?: any;
  error?: string;
  message?: string;
  executionTime?: string;
}

export function KnowledgeBaseMessageCard({
  className,
  message,
}: {
  className?: string;
  message: Message;
}) {
  const data = useMemo<KnowledgeBaseData>(() => {
    try {
      return JSON.parse(message.content || "{}");
    } catch {
      return {} as KnowledgeBaseData;
    }
  }, [message.content]);

  const isBuilding = useMemo(() => {
    return message.isStreaming || data.status === "building" || data.status === "starting";
  }, [message.isStreaming, data.status]);

  const hasError = useMemo(() => {
    return data.status === "error" || !!data.error;
  }, [data.status, data.error]);

  const isComplete = useMemo(() => {
    return data.status === "complete";
  }, [data.status]);

  const isCancelled = useMemo(() => {
    return data.status === "cancelled";
  }, [data.status]);

  const progress = useMemo(() => {
    return data.currentStatus?.progress_percentage || 0;
  }, [data.currentStatus]);

  const statusIcon = useMemo(() => {
    if (isBuilding) return <LoadingOutlined className="animate-spin" />;
    if (hasError) return <XCircle className="text-red-500" size={16} />;
    if (isCancelled) return <AlertCircle className="text-yellow-500" size={16} />;
    if (isComplete) return <CheckCircle2 className="text-green-500" size={16} />;
    return <Database size={16} />;
  }, [isBuilding, hasError, isCancelled, isComplete]);

  const statusText = useMemo(() => {
    if (isBuilding) return "Building knowledge base...";
    if (hasError) return "Failed to build knowledge base";
    if (isCancelled) return "Knowledge base build cancelled";
    if (isComplete) return "Knowledge base built successfully";
    return "Knowledge base";
  }, [isBuilding, hasError, isCancelled, isComplete]);

  const statusColor = useMemo(() => {
    if (hasError) return "text-red-500";
    if (isCancelled) return "text-yellow-500";
    if (isComplete) return "text-green-500";
    return "";
  }, [hasError, isCancelled, isComplete]);

  return (
    <Card className={cn("w-full max-w-[600px]", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {statusIcon}
            <RainbowText animated={isBuilding} className={statusColor}>
              {statusText}
            </RainbowText>
          </div>
          {data.currentStatus && isBuilding && (
            <div className="text-sm text-muted-foreground">
              {data.currentStatus.processed_article_count} / {data.currentStatus.target_article_count}
            </div>
          )}
        </div>
        <CardTitle>
          <div className="text-lg font-medium">
            Knowledge Base: {data.request?.topics?.join(", ")}
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Request Summary */}
        <div className="flex flex-wrap gap-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <FileText size={12} />
            {data.request?.target_article_count} articles
          </Badge>
          {data.request?.industries && data.request.industries.length > 0 && (
            <Badge variant="outline" className="flex items-center gap-1">
              <Globe size={12} />
              {data.request.industries.join(", ")}
            </Badge>
          )}
          <Badge variant="outline">
            {data.request?.locale || "en-US"}
          </Badge>
        </div>

        {/* Progress Bar */}
        {isBuilding && data.currentStatus && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="w-full" />
            
            {/* Current Plan */}
            {data.currentStatus.current_plan_title && (
              <div className="text-sm text-muted-foreground">
                <Clock size={12} className="inline mr-1" />
                {data.currentStatus.current_plan_title}
              </div>
            )}
          </div>
        )}

        {/* URL Status */}
        {data.currentStatus && (
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Processed:</span>
                <span className="font-medium text-green-600">
                  {data.currentStatus.processed_urls}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Processing:</span>
                <span className="font-medium text-blue-600">
                  {data.currentStatus.processing_urls}
                </span>
              </div>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Pending:</span>
                <span className="font-medium text-yellow-600">
                  {data.currentStatus.pending_urls}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Failed:</span>
                <span className="font-medium text-red-600">
                  {data.currentStatus.failed_urls}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Topic Breakdown */}
        {data.currentStatus?.article_counts_by_topic && 
         Object.keys(data.currentStatus.article_counts_by_topic).length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <TrendingUp size={12} />
              Articles by Topic
            </div>
            <div className="space-y-1">
              {Object.entries(data.currentStatus.article_counts_by_topic).map(([topic, count]) => (
                <div key={topic} className="flex justify-between text-sm">
                  <span className="truncate">{topic}</span>
                  <span className="font-medium">{count}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Final Results */}
        {isComplete && data.finalStatus && (
          <motion.div 
            className="rounded-lg bg-green-50 p-3 text-sm"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="font-medium text-green-800 mb-2">
              ✅ Successfully acquired {data.finalStatus.processed_article_count} articles
            </div>
            {data.executionTime && (
              <div className="text-green-600">
                Completed in {new Date(data.executionTime).toLocaleTimeString()}
              </div>
            )}
          </motion.div>
        )}

        {/* Error Display */}
        {hasError && (
          <motion.div 
            className="rounded-lg bg-red-50 p-3 text-sm"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="font-medium text-red-800 mb-1">
              ❌ Build Failed
            </div>
            <div className="text-red-600">
              {data.error || data.currentStatus?.last_error || "Unknown error occurred"}
            </div>
          </motion.div>
        )}

        {/* Cancellation Display */}
        {isCancelled && (
          <motion.div 
            className="rounded-lg bg-yellow-50 p-3 text-sm"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="font-medium text-yellow-800">
              ⚠️ Build was cancelled by user
            </div>
          </motion.div>
        )}

        {/* Current Message */}
        {data.message && isBuilding && (
          <div className="text-sm text-muted-foreground italic">
            {data.message}
          </div>
        )}
      </CardContent>
    </Card>
  );
}