# src/server/kb_request.py
# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class BuildKnowledgeBaseRequest(BaseModel):
    """Request model for building a knowledge base."""
    
    topics: List[str] = Field(
        ..., 
        description="List of topics to build knowledge about",
        min_items=1,
        example=["artificial intelligence", "machine learning"]
    )
    target_article_count: int = Field(
        10, 
        description="Target number of articles to acquire",
        ge=1,
        le=100
    )
    industries: Optional[List[str]] = Field(
        None,
        description="List of industries to focus on (optional)",
        example=["technology", "healthcare"]
    )
    locale: str = Field(
        "en-US",
        description="Language locale for content acquisition",
        example="en-US"
    )
    max_step_num: Optional[int] = Field(
        5,
        description="Maximum number of planning steps",
        ge=1,
        le=10
    )
    max_search_results: Optional[int] = Field(
        5,
        description="Maximum search results per query", 
        ge=1,
        le=20
    )


class KnowledgeBaseStatusResponse(BaseModel):
    """Response model for knowledge base status."""
    
    target_article_count: int = Field(..., description="Target number of articles")
    processed_article_count: int = Field(..., description="Number of articles processed")
    progress_percentage: float = Field(..., description="Completion percentage")
    pending_urls: int = Field(..., description="Number of pending URLs")
    processing_urls: int = Field(..., description="Number of URLs being processed")
    processed_urls: int = Field(..., description="Number of successfully processed URLs")
    failed_urls: int = Field(..., description="Number of failed URLs")
    article_counts_by_topic: Dict[str, int] = Field(..., description="Article counts by topic")
    article_counts_by_industry: Dict[str, int] = Field(..., description="Article counts by industry")
    current_plan_title: Optional[str] = Field(None, description="Current plan title")
    plan_iterations: int = Field(..., description="Number of planning iterations")
    last_error: Optional[str] = Field(None, description="Last error message if any")


class BuildKnowledgeBaseResponse(BaseModel):
    """Response model for knowledge base building results."""
    
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Human-readable result message")
    final_status: Optional[KnowledgeBaseStatusResponse] = Field(
        None, 
        description="Final status after completion"
    )
    error: Optional[str] = Field(None, description="Error message if operation failed")
    execution_time_seconds: Optional[float] = Field(
        None, 
        description="Total execution time in seconds"
    )


class KnowledgeBaseStreamEvent(BaseModel):
    """Event model for streaming knowledge base building progress."""
    
    event_type: str = Field(..., description="Type of event (status_update, error, complete)")
    timestamp: str = Field(..., description="ISO timestamp of the event")
    node: Optional[str] = Field(None, description="Graph node that generated the event")
    status: Optional[KnowledgeBaseStatusResponse] = Field(None, description="Current status")
    message: Optional[str] = Field(None, description="Event message")
    error: Optional[str] = Field(None, description="Error message if applicable")